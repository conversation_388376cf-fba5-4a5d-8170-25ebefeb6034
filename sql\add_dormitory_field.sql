-- 为students表添加宿舍号字段的SQL脚本
-- 执行前请先备份数据库

-- 检查dormitory字段是否已存在
SELECT COLUMN_NAME 
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'students' 
  AND COLUMN_NAME = 'dormitory';

-- 如果上述查询没有返回结果，则执行以下语句添加字段
-- 为students表添加宿舍号字段
ALTER TABLE `students` 
ADD COLUMN `dormitory` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '宿舍号' 
AFTER `class_code`;

-- 创建宿舍号索引以提高查询性能
CREATE INDEX `idx_dormitory` ON `students` (`dormitory`);

-- 更新一些测试数据的宿舍号（示例）
UPDATE `students` SET `dormitory` = '101-201' WHERE `student_id` = '202305480137';
UPDATE `students` SET `dormitory` = '101-202' WHERE `student_id` LIKE '202305480%' AND `dormitory` IS NULL LIMIT 5;
UPDATE `students` SET `dormitory` = '101-203' WHERE `student_id` LIKE '202305481%' AND `dormitory` IS NULL LIMIT 5;
UPDATE `students` SET `dormitory` = '102-201' WHERE `student_id` LIKE '202305482%' AND `dormitory` IS NULL LIMIT 5;
UPDATE `students` SET `dormitory` = '102-202' WHERE `student_id` LIKE '202305483%' AND `dormitory` IS NULL LIMIT 5;

-- 验证字段添加成功
SELECT COUNT(*) as total_students, 
       COUNT(dormitory) as students_with_dormitory,
       COUNT(*) - COUNT(dormitory) as students_without_dormitory
FROM `students`;

-- 查看宿舍号分布
SELECT dormitory, COUNT(*) as student_count 
FROM `students` 
WHERE dormitory IS NOT NULL 
GROUP BY dormitory 
ORDER BY dormitory;
