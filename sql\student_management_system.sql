/*
 学生信息管理系统数据库设计

 主要模块：
 1. 学院管理
 2. 专业管理
 3. 班级管理
 4. 学年学期管理
 5. 课程管理
 6. 教师管理
 7. 学生管理
 8. 期末成绩管理
 9. 基本素质测评管理

 设计日期: 2025-07-26
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- 1. 学院表
-- ----------------------------
DROP TABLE IF EXISTS `colleges`;
CREATE TABLE `colleges` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '学院ID',
  `college_code` varchar(20) NOT NULL COMMENT '学院代码',
  `college_name` varchar(100) NOT NULL COMMENT '学院名称',
  `description` text COMMENT '学院描述',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_college_code` (`college_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='学院信息表';

-- ----------------------------
-- 2. 专业表
-- ----------------------------
DROP TABLE IF EXISTS `majors`;
CREATE TABLE `majors` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '专业ID',
  `major_code` varchar(20) NOT NULL COMMENT '专业代码',
  `major_name` varchar(100) NOT NULL COMMENT '专业名称',
  `college_code` varchar(20) NOT NULL COMMENT '所属学院代码',
  `duration` tinyint NOT NULL DEFAULT 3 COMMENT '学制年限',
  `description` text COMMENT '专业描述',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_major_code` (`major_code`),
  KEY `idx_college_code` (`college_code`),
  CONSTRAINT `fk_majors_college` FOREIGN KEY (`college_code`) REFERENCES `colleges` (`college_code`) ON DELETE RESTRICT ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='专业信息表';

-- ----------------------------
-- 3. 学年学期表
-- ----------------------------
DROP TABLE IF EXISTS `semesters`;
CREATE TABLE `semesters` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '学期ID',
  `academic_year` varchar(20) NOT NULL COMMENT '学年，如2023-2024',
  `semester_number` tinyint NOT NULL COMMENT '学期号，1或2',
  `semester_name` varchar(50) NOT NULL COMMENT '学期名称',
  `start_date` date NOT NULL COMMENT '开始日期',
  `end_date` date NOT NULL COMMENT '结束日期',
  `is_current` tinyint(1) DEFAULT 0 COMMENT '是否当前学期',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_academic_semester` (`academic_year`, `semester_number`),
  KEY `idx_is_current` (`is_current`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='学年学期表';

-- ----------------------------
-- 4. 教师表
-- ----------------------------
DROP TABLE IF EXISTS `teachers`;
CREATE TABLE `teachers` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '教师ID',
  `teacher_code` varchar(20) NOT NULL COMMENT '教师工号',
  `name` varchar(50) NOT NULL COMMENT '姓名',
  `gender` enum('男','女') NOT NULL COMMENT '性别',
  `birth_date` date COMMENT '出生日期',
  `phone` varchar(20) COMMENT '电话号码',
  `email` varchar(100) COMMENT '邮箱',
  `college_code` varchar(20) NOT NULL COMMENT '所属学院代码',
  `title` varchar(50) COMMENT '职称',
  `hire_date` date COMMENT '入职日期',
  `status` enum('在职','离职','退休') DEFAULT '在职' COMMENT '状态',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_teacher_code` (`teacher_code`),
  KEY `idx_college_code` (`college_code`),
  KEY `idx_status` (`status`),
  CONSTRAINT `fk_teachers_college` FOREIGN KEY (`college_code`) REFERENCES `colleges` (`college_code`) ON DELETE RESTRICT ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='教师信息表';

-- ----------------------------
-- 5. 班级表
-- ----------------------------
DROP TABLE IF EXISTS `classes`;
CREATE TABLE `classes` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '班级ID',
  `class_code` varchar(20) NOT NULL COMMENT '班级代码',
  `class_name` varchar(100) NOT NULL COMMENT '班级名称',
  `major_code` varchar(20) NOT NULL COMMENT '所属专业代码',
  `grade_year` year NOT NULL COMMENT '入学年份',
  `student_count` int DEFAULT 0 COMMENT '学生人数',
  `head_teacher_code` varchar(20) COMMENT '班主任工号',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_class_code` (`class_code`),
  KEY `idx_major_code` (`major_code`),
  KEY `idx_head_teacher_code` (`head_teacher_code`),
  KEY `idx_grade_year` (`grade_year`),
  CONSTRAINT `fk_classes_major` FOREIGN KEY (`major_code`) REFERENCES `majors` (`major_code`) ON DELETE RESTRICT ON UPDATE CASCADE,
  CONSTRAINT `fk_classes_head_teacher` FOREIGN KEY (`head_teacher_code`) REFERENCES `teachers` (`teacher_code`) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='班级信息表';

-- ----------------------------
-- 6. 学生表
-- ----------------------------
DROP TABLE IF EXISTS `students`;
CREATE TABLE `students` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '学生ID',
  `student_id` varchar(20) NOT NULL COMMENT '学号',
  `name` varchar(50) NOT NULL COMMENT '姓名',
  `gender` enum('男','女') NOT NULL COMMENT '性别',
  `birth_date` date COMMENT '出生日期',
  `id_card` varchar(18) COMMENT '身份证号',
  `phone` varchar(20) COMMENT '电话号码',
  `email` varchar(100) COMMENT '邮箱',
  `class_code` varchar(20) NOT NULL COMMENT '所属班级代码',
  `enrollment_date` date NOT NULL COMMENT '入学日期',
  `status` enum('在校','毕业','退学','休学','转学') DEFAULT '在校' COMMENT '学籍状态',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_student_id` (`student_id`),
  KEY `idx_class_code` (`class_code`),
  KEY `idx_status` (`status`),
  CONSTRAINT `fk_students_class` FOREIGN KEY (`class_code`) REFERENCES `classes` (`class_code`) ON DELETE RESTRICT ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='学生信息表';

-- ----------------------------
-- 7. 课程表（优化版）
-- ----------------------------
DROP TABLE IF EXISTS `courses`;
CREATE TABLE `courses` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '课程ID',
  `course_code` varchar(20) NOT NULL COMMENT '课程代码',
  `course_name` varchar(100) NOT NULL COMMENT '课程名称',
  `credits` decimal(4,2) NOT NULL COMMENT '学分',
  `course_type` enum('必修','选修','实践','通识') DEFAULT '必修' COMMENT '课程类型',
  `major_code` varchar(20) COMMENT '所属专业代码（通识课可为空）',
  `description` text COMMENT '课程描述',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_course_code` (`course_code`),
  KEY `idx_major_code` (`major_code`),
  KEY `idx_course_type` (`course_type`),
  CONSTRAINT `fk_courses_major` FOREIGN KEY (`major_code`) REFERENCES `majors` (`major_code`) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='课程信息表';

-- ----------------------------
-- 8. 课程-教师关联表
-- ----------------------------
DROP TABLE IF EXISTS `course_teachers`;
CREATE TABLE `course_teachers` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `course_code` varchar(20) NOT NULL COMMENT '课程代码',
  `teacher_code` varchar(20) NOT NULL COMMENT '教师工号',
  `semester_id` int NOT NULL COMMENT '学期ID',
  `class_code` varchar(20) NOT NULL COMMENT '班级代码',
  `is_primary` tinyint(1) DEFAULT 1 COMMENT '是否主讲教师',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_course_teacher_semester_class` (`course_code`, `teacher_code`, `semester_id`, `class_code`),
  KEY `idx_teacher_code` (`teacher_code`),
  KEY `idx_semester_id` (`semester_id`),
  KEY `idx_class_code` (`class_code`),
  CONSTRAINT `fk_course_teachers_course` FOREIGN KEY (`course_code`) REFERENCES `courses` (`course_code`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `fk_course_teachers_teacher` FOREIGN KEY (`teacher_code`) REFERENCES `teachers` (`teacher_code`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `fk_course_teachers_semester` FOREIGN KEY (`semester_id`) REFERENCES `semesters` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `fk_course_teachers_class` FOREIGN KEY (`class_code`) REFERENCES `classes` (`class_code`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='课程教师关联表';

-- ----------------------------
-- 9. 期末成绩表（简化版）
-- ----------------------------
DROP TABLE IF EXISTS `grades`;
CREATE TABLE `grades` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '成绩ID',
  `student_id` varchar(20) NOT NULL COMMENT '学号',
  `course_code` varchar(20) NOT NULL COMMENT '课程代码',
  `semester_id` int NOT NULL COMMENT '学期ID',
  `final_score` decimal(5,2) NOT NULL COMMENT '总成绩',
  `grade_point` decimal(3,2) COMMENT '绩点',
  `is_retake` tinyint(1) DEFAULT 0 COMMENT '是否重修',
  `remarks` varchar(255) COMMENT '备注',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_student_course_semester` (`student_id`, `course_code`, `semester_id`, `is_retake`),
  KEY `idx_course_code` (`course_code`),
  KEY `idx_semester_id` (`semester_id`),
  KEY `idx_final_score` (`final_score`),
  CONSTRAINT `fk_grades_student` FOREIGN KEY (`student_id`) REFERENCES `students` (`student_id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `fk_grades_course` FOREIGN KEY (`course_code`) REFERENCES `courses` (`course_code`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `fk_grades_semester` FOREIGN KEY (`semester_id`) REFERENCES `semesters` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='期末成绩表';

-- 基本素质测评表（匹配实际数据库结构）
DROP TABLE IF EXISTS `quality_evaluation`;
CREATE TABLE `quality_evaluation` (
                                      `evaluation_id` int NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                                      `student_id` varchar(20) NOT NULL COMMENT '学号',
                                      `add_score` decimal(6,2) DEFAULT 0 COMMENT '加分项',
                                      `reduce_score` decimal(6,2) DEFAULT 0 COMMENT '扣分项',
                                      `add_score_remark` varchar(500) DEFAULT NULL COMMENT '加分说明',
                                      `reduce_score_remark` varchar(500) DEFAULT NULL COMMENT '扣分说明',
                                      `semester_id` int DEFAULT NULL COMMENT '学期ID（关联semesters表）',
                                      `period_score` decimal(6,2) DEFAULT 0 COMMENT '周期得分',
                                      `total_score` decimal(6,2) DEFAULT 0 COMMENT '总得分',
                                      `prev_period_score` decimal(6,2) DEFAULT NULL COMMENT '上一学期总分（仅在当前为第二学期时有值）',
                                      `class_ranking` int DEFAULT NULL COMMENT '班级排名',
                                      `major_ranking` int DEFAULT NULL COMMENT '专业排名',
                                      `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                      `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                      PRIMARY KEY (`evaluation_id`),
                                      UNIQUE KEY `uk_student_semester` (`student_id`, `semester_id`),
                                      KEY `idx_student_id` (`student_id`),
                                      KEY `idx_semester_id` (`semester_id`),
                                      KEY `idx_total_score` (`total_score`),
                                      CONSTRAINT `fk_quality_evaluation_student` FOREIGN KEY (`student_id`) REFERENCES `students` (`student_id`) ON DELETE CASCADE ON UPDATE CASCADE,
                                      CONSTRAINT `fk_quality_evaluation_semester` FOREIGN KEY (`semester_id`) REFERENCES `semesters` (`id`) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='基本素质测评成绩表';

-- ----------------------------
-- 示例数据插入
-- ----------------------------

-- 插入学院数据
INSERT INTO `colleges` (`college_code`, `college_name`, `description`) VALUES
('CS', '计算机学院', '计算机科学与技术相关专业'),
('EE', '电子工程学院', '电子信息工程相关专业'),
('ME', '机械工程学院', '机械制造与自动化相关专业'),
('BA', '商学院', '工商管理相关专业');

-- 插入专业数据
INSERT INTO `majors` (`major_code`, `major_name`, `college_code`, `duration`, `description`) VALUES
('CS01', '计算机应用技术', 'CS', 3, '培养计算机应用技术人才'),
('CS02', '软件技术', 'CS', 3, '培养软件开发技术人才'),
('EE01', '电子信息工程技术', 'EE', 3, '培养电子信息技术人才'),
('ME01', '机械制造与自动化', 'ME', 3, '培养机械制造技术人才'),
('BA01', '工商企业管理', 'BA', 3, '培养企业管理人才');

-- 插入学期数据
-- 注意：这里会自动生成 id，用于 quality_evaluation 表的 semester_id 外键
INSERT INTO `semesters` (`id`, `academic_year`, `semester_number`, `semester_name`, `start_date`, `end_date`, `is_current`) VALUES
(1, '2022-2023', 1, '2022-2023学年第一学期', '2022-09-01', '2023-01-15', 0),
(2, '2022-2023', 2, '2022-2023学年第二学期', '2023-02-20', '2023-07-10', 0),
(3, '2023-2024', 1, '2023-2024学年第一学期', '2023-09-01', '2024-01-15', 0),
(4, '2023-2024', 2, '2023-2024学年第二学期', '2024-02-20', '2024-07-10', 0),
(5, '2024-2025', 1, '2024-2025学年第一学期', '2024-09-01', '2025-01-15', 1),
(6, '2024-2025', 2, '2024-2025学年第二学期', '2025-02-20', '2025-07-10', 0);

-- 插入教师数据
INSERT INTO `teachers` (`teacher_code`, `name`, `gender`, `college_code`, `title`, `hire_date`, `status`) VALUES
('T001', '张教授', '男', 'CS', '教授', '2010-09-01', '在职'),
('T002', '李副教授', '女', 'CS', '副教授', '2015-03-01', '在职'),
('T003', '王讲师', '男', 'CS', '讲师', '2018-09-01', '在职'),
('T004', '刘教授', '女', 'EE', '教授', '2012-09-01', '在职'),
('T005', '陈副教授', '男', 'EE', '副教授', '2016-03-01', '在职');

-- 插入班级数据
INSERT INTO `classes` (`class_code`, `class_name`, `major_code`, `grade_year`, `student_count`, `head_teacher_code`) VALUES
('CS2023-01', '计算机应用技术2023级1班', 'CS01', 2023, 45, 'T001'),
('CS2023-02', '计算机应用技术2023级2班', 'CS01', 2023, 43, 'T002'),
('SW2023-01', '软件技术2023级1班', 'CS02', 2023, 40, 'T003'),
('EE2023-01', '电子信息工程技术2023级1班', 'EE01', 2023, 38, 'T004'),
('ME2023-01', '机械制造与自动化2023级1班', 'ME01', 2023, 42, 'T005');

-- 插入学生数据（示例）
INSERT INTO `students` (`student_id`, `name`, `gender`, `class_code`, `enrollment_date`, `status`) VALUES
('202305480101', '张三', '男', 'CS2023-01', '2023-09-01', '在校'),
('202305480102', '李四', '女', 'CS2023-01', '2023-09-01', '在校'),
('202305480103', '王五', '男', 'CS2023-02', '2023-09-01', '在校');

-- 插入学生数据（示例）
INSERT INTO `students` (`student_id`, `name`, `gender`, `class_id`, `enrollment_date`, `status`) VALUES
('202305480101', '张三', '男', 1, '2023-09-01', '在校'),
('202305480102', '李四', '女', 1, '2023-09-01', '在校'),
('202305480103', '王五', '男', 2, '2023-09-01', '在校');

-- 插入素质测评数据（示例）
-- 注意：semester_id 对应 semesters 表的实际 ID
-- ID=3 对应 '2023-2024学年第一学期'，ID=4 对应 '2023-2024学年第二学期'
INSERT INTO `quality_evaluation` (`student_id`, `semester_id`, `add_score`, `reduce_score`, `add_score_remark`, `reduce_score_remark`, `period_score`, `total_score`, `class_ranking`, `major_ranking`) VALUES
('202305480101', 3, 5.0, 0.0, '参加志愿活动', NULL, 85.0, 90.0, 1, 1),
('202305480102', 3, 3.0, 2.0, '获得奖学金', '迟到扣分', 80.0, 81.0, 2, 3),
('202305480103', 3, 2.0, 1.0, '参加比赛', '违纪扣分', 75.0, 76.0, 3, 5),
('202305480101', 4, 4.0, 0.0, '社会实践', NULL, 88.0, 92.0, 1, 1),
('202305480102', 4, 2.0, 0.0, '学习进步', NULL, 82.0, 84.0, 2, 2);

SET FOREIGN_KEY_CHECKS = 1;
