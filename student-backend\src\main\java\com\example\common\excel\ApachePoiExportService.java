package com.example.common.excel;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * Apache POI 流式Excel导出服务
 * 专门用于处理大数据量（百万行）的Excel导出
 * 使用SXSSF（Streaming Usermodel API）来优化内存使用
 * 支持表头高度自适应和学业成绩、绩点保留2位小数
 *
 * <AUTHOR>
 * @since 2025-01-30
 */
@Service
@Slf4j
public class ApachePoiExportService {

    /**
     * 内存中保留的行数（窗口大小）
     * 超过这个数量的行会被写入临时文件，释放内存
     */
    private static final int WINDOW_SIZE = 1000;

    /**
     * 导入结果类
     */
    @Data
    public static class ImportResult<T> {
        private boolean success;
        private String message;
        private int successCount;
        private int errorCount;
        private List<T> successData;
        private List<ImportError> errors;

        public ImportResult() {
            this.successData = new ArrayList<>();
            this.errors = new ArrayList<>();
        }
    }

    /**
     * 导入错误类
     */
    @Data
    public static class ImportError {
        private int rowNumber;
        private String errorMessage;

        public ImportError(int rowNumber, String errorMessage) {
            this.rowNumber = rowNumber;
            this.errorMessage = errorMessage;
        }
    }

    /**
     * 导出成绩数据到Excel（支持百万行数据）
     *
     * @param data      导出数据
     * @param headers   表头映射 (字段名 -> 显示名称)
     * @param sheetName 工作表名称
     * @return Excel文件字节数组
     */
    public byte[] exportGradesToExcel(List<Map<String, Object>> data, Map<String, String> headers, String sheetName) {
        log.info("开始Apache POI流式导出，数据行数: {}, 表头数量: {}", data.size(), headers.size());

        // 使用SXSSF工作簿，支持大数据量导出
        try (SXSSFWorkbook workbook = new SXSSFWorkbook(WINDOW_SIZE);
             ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {

            // 创建工作表
            Sheet sheet = workbook.createSheet(sheetName);

            // 创建样式
            CellStyle headerStyle = ExcelUtils.createHeaderStyle(workbook);
            CellStyle dataStyle = ExcelUtils.createDataStyle(workbook);
            CellStyle numberStyle = ExcelUtils.createNumberStyle(workbook);
            CellStyle wrapTextStyle = ExcelUtils.createWrapTextStyle(workbook);

            // 构建字段顺序
            List<String> fieldOrder = new ArrayList<>(headers.keySet());

            // 创建表头行
            createHeaderRow(sheet, headers, fieldOrder, headerStyle);

            // 填充数据行
            fillDataRows(sheet, data, fieldOrder, dataStyle, numberStyle, wrapTextStyle);

            // 自动调整所有列宽
            ExcelUtils.autoSizeColumns(sheet, fieldOrder.size());

            // 写入输出流
            workbook.write(outputStream);

            byte[] result = outputStream.toByteArray();
            log.info("Apache POI导出成功，数据行数: {}, 文件大小: {} bytes", data.size(), result.length);

            return result;

        } catch (IOException e) {
            log.error("Apache POI导出失败", e);
            throw new RuntimeException("Excel导出失败: " + e.getMessage(), e);
        }
    }



    /**
     * 创建表头行（基于Map）
     */
    private void createHeaderRow(Sheet sheet, Map<String, String> headers, List<String> fieldOrder, CellStyle headerStyle) {
        // 转换为数组格式
        String[] headerArray = new String[fieldOrder.size()];
        for (int i = 0; i < fieldOrder.size(); i++) {
            String fieldName = fieldOrder.get(i);
            headerArray[i] = headers.get(fieldName) != null ? headers.get(fieldName) : fieldName;
        }

        // 调用统一的数组版本方法
        createHeaderRowFromArray(sheet, headerArray, headerStyle);
    }

    /**
     * 填充数据行
     */
    private void fillDataRows(Sheet sheet, List<Map<String, Object>> data, List<String> fieldOrder, CellStyle dataStyle, CellStyle numberStyle, CellStyle wrapTextStyle) {
        for (int rowIndex = 0; rowIndex < data.size(); rowIndex++) {
            Map<String, Object> rowData = data.get(rowIndex);
            Row row = sheet.createRow(rowIndex + 1); // +1 因为第0行是表头

            // 计算当前行内容的最大行数，用于自适应行高
            int maxLines = 1;
            for (String fieldName : fieldOrder) {
                Object value = rowData.get(fieldName);
                if (value != null && value.toString().contains("\n")) {
                    int lines = value.toString().split("\n").length;
                    maxLines = Math.max(maxLines, lines);
                }
            }

            // 设置合理的行高 - 减小行高计算值
            if (maxLines > 1) {
                // 调整行高计算公式，使其更合理：每行约250单位 + 基础高度150
                short rowHeight = (short) (maxLines * 250 + 150);
                // 设置最大行高限制，避免过高
                if (rowHeight > 1000) {
                    rowHeight = 1000;
                }
                row.setHeight(rowHeight);
            } else {
                // 为单行内容设置标准行高（约15磅，对应300单位）
                row.setHeight((short) 300);
            }

            for (int colIndex = 0; colIndex < fieldOrder.size(); colIndex++) {
                String fieldName = fieldOrder.get(colIndex);
                Object value = rowData.get(fieldName);

                Cell cell = row.createCell(colIndex);
                ExcelUtils.setCellValue(cell, value);

                // 根据字段名判断使用哪种样式
                if (isScoreOrGpaField(fieldName) && value instanceof Number) {
                    cell.setCellStyle(numberStyle);
                } else if (isRemarkField(fieldName)) {
                    // 加分说明和扣分说明字段使用自动换行样式
                    cell.setCellStyle(wrapTextStyle);
                } else {
                    cell.setCellStyle(dataStyle);
                }
            }
        }
    }

    /**
     * 判断是否为学业成绩或绩点字段
     */
    private boolean isScoreOrGpaField(String fieldName) {
        if (fieldName == null) return false;
        String lowerFieldName = fieldName.toLowerCase();
        return lowerFieldName.contains("score") ||
               lowerFieldName.contains("grade") ||
               lowerFieldName.contains("gpa") ||
               lowerFieldName.contains("绩点") ||
               lowerFieldName.contains("成绩") ||
               lowerFieldName.contains("分数");
    }
    /**
     * 判断是否为说明字段（需要自动换行）
     */
    private boolean isRemarkField(String fieldName) {
        return fieldName != null && (
                fieldName.equals("addScoreRemark") ||
                fieldName.equals("reduceScoreRemark") ||
                fieldName.toLowerCase().contains("remark") ||
                fieldName.toLowerCase().contains("comment") ||
                fieldName.toLowerCase().contains("description")
        );
    }





    /**
     * 生成Excel模板
     *
     * @param headers   表头映射 (字段名 -> 显示名称)
     * @param sheetName 工作表名称
     * @return Excel模板文件字节数组
     */
    public byte[] generateTemplate(Map<String, String> headers, String sheetName) {
        log.info("开始生成Excel模板，表头数量: {}", headers.size());

        try (SXSSFWorkbook workbook = new SXSSFWorkbook(WINDOW_SIZE);
             ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {

            // 创建工作表
            Sheet sheet = workbook.createSheet(sheetName);

            // 创建样式
            CellStyle headerStyle = ExcelUtils.createHeaderStyle(workbook);

            // 构建字段顺序
            List<String> fieldOrder = new ArrayList<>(headers.keySet());

            // 创建表头行
            createHeaderRow(sheet, headers, fieldOrder, headerStyle);

            // 自动调整所有列宽
            ExcelUtils.autoSizeColumns(sheet, fieldOrder.size());

            // 写入输出流
            workbook.write(outputStream);

            byte[] result = outputStream.toByteArray();
            log.info("Excel模板生成成功，文件大小: {} bytes", result.length);

            return result;

        } catch (Exception e) {
            log.error("生成Excel模板失败", e);
            throw new RuntimeException("生成Excel模板失败: " + e.getMessage());
        }
    }

    /**
     * 导入Excel数据
     *
     * @param file      上传的Excel文件
     * @param headers   表头映射 (字段名 -> 显示名称)
     * @param sheetName 工作表名称
     * @return 导入的数据列表
     */
    public List<Map<String, Object>> importExcelData(MultipartFile file, Map<String, String> headers, String sheetName) {
        log.info("开始导入Excel数据，文件: {}, 大小: {} bytes", file.getOriginalFilename(), file.getSize());

        // 验证文件
        validateExcelFile(file);

        try (InputStream inputStream = file.getInputStream();
             Workbook workbook = new XSSFWorkbook(inputStream)) {

            Sheet sheet = workbook.getSheet(sheetName);
            if (sheet == null) {
                sheet = workbook.getSheetAt(0); // 如果指定工作表不存在，使用第一个工作表
            }

            // 验证表头
            validateHeaders(sheet, headers);

            // 读取数据
            List<Map<String, Object>> dataList = readDataFromSheet(sheet, headers);

            log.info("Excel数据导入成功，共读取 {} 行数据", dataList.size());
            return dataList;

        } catch (Exception e) {
            log.error("导入Excel数据失败", e);
            throw new RuntimeException("导入Excel数据失败: " + e.getMessage());
        }
    }

    /**
     * 验证Excel文件
     */
    private void validateExcelFile(MultipartFile file) {
        if (file == null || file.isEmpty()) {
            throw new IllegalArgumentException("文件不能为空");
        }

        String fileName = file.getOriginalFilename();
        if (fileName == null || (!fileName.endsWith(".xlsx") && !fileName.endsWith(".xls"))) {
            throw new IllegalArgumentException("请上传Excel文件（.xlsx或.xls格式）");
        }

        // 文件大小限制（10MB）
        if (file.getSize() > 10 * 1024 * 1024) {
            throw new IllegalArgumentException("文件大小不能超过10MB");
        }
    }

    /**
     * 验证表头
     */
    private void validateHeaders(Sheet sheet, Map<String, String> headers) {
        Row headerRow = sheet.getRow(0);
        if (headerRow == null) {
            throw new IllegalArgumentException("Excel文件表头不能为空");
        }

        List<String> expectedHeaders = new ArrayList<>(headers.values());
        List<String> actualHeaders = new ArrayList<>();

        for (Cell cell : headerRow) {
            if (cell != null) {
                actualHeaders.add(getCellStringValue(cell));
            }
        }

        // 检查必需的表头是否存在
        for (String expectedHeader : expectedHeaders) {
            if (!actualHeaders.contains(expectedHeader)) {
                throw new IllegalArgumentException("缺少必需的表头: " + expectedHeader);
            }
        }
    }

    /**
     * 从工作表读取数据
     */
    private List<Map<String, Object>> readDataFromSheet(Sheet sheet, Map<String, String> headers) {
        List<Map<String, Object>> dataList = new ArrayList<>();

        // 获取表头行
        Row headerRow = sheet.getRow(0);
        if (headerRow == null) {
            return dataList;
        }

        // 建立表头索引映射
        Map<String, Integer> headerIndexMap = buildHeaderIndexMap(headerRow, headers);

        // 读取数据行
        for (int rowIndex = 1; rowIndex <= sheet.getLastRowNum(); rowIndex++) {
            Row row = sheet.getRow(rowIndex);
            if (row == null || isEmptyRow(row)) {
                continue;
            }

            Map<String, Object> rowData = new LinkedHashMap<>();
            boolean hasData = false;

            for (Map.Entry<String, String> entry : headers.entrySet()) {
                String fieldName = entry.getKey();
                String headerName = entry.getValue();
                Integer columnIndex = headerIndexMap.get(headerName);

                if (columnIndex != null) {
                    Cell cell = row.getCell(columnIndex);
                    Object value = getCellValue(cell);
                    rowData.put(fieldName, value);
                    if (value != null && !value.toString().trim().isEmpty()) {
                        hasData = true;
                    }
                }
            }

            if (hasData) {
                dataList.add(rowData);
            }
        }

        return dataList;
    }

    /**
     * 建立表头索引映射
     */
    private Map<String, Integer> buildHeaderIndexMap(Row headerRow, Map<String, String> headers) {
        Map<String, Integer> indexMap = new LinkedHashMap<>();

        for (Cell cell : headerRow) {
            if (cell != null) {
                String headerName = getCellStringValue(cell);
                if (headers.containsValue(headerName)) {
                    indexMap.put(headerName, cell.getColumnIndex());
                }
            }
        }

        return indexMap;
    }

    /**
     * 判断行是否为空
     */
    private boolean isEmptyRow(Row row) {
        for (Cell cell : row) {
            if (cell != null && cell.getCellType() != CellType.BLANK) {
                String value = getCellStringValue(cell);
                if (value != null && !value.trim().isEmpty()) {
                    return false;
                }
            }
        }
        return true;
    }

    /**
     * 获取单元格值
     */
    private Object getCellValue(Cell cell) {
        if (cell == null) {
            return null;
        }

        switch (cell.getCellType()) {
            case STRING:
                return cell.getStringCellValue().trim();
            case NUMERIC:
                if (DateUtil.isCellDateFormatted(cell)) {
                    return cell.getDateCellValue();
                } else {
                    double numericValue = cell.getNumericCellValue();
                    // 如果是整数，返回Long，否则返回BigDecimal
                    if (numericValue == Math.floor(numericValue)) {
                        return (long) numericValue;
                    } else {
                        return BigDecimal.valueOf(numericValue);
                    }
                }
            case BOOLEAN:
                return cell.getBooleanCellValue();
            case FORMULA:
                try {
                    return cell.getNumericCellValue();
                } catch (Exception e) {
                    return cell.getStringCellValue();
                }
            default:
                return null;
        }
    }

    /**
     * 获取单元格字符串值
     */
    private String getCellStringValue(Cell cell) {
        if (cell == null) {
            return "";
        }

        switch (cell.getCellType()) {
            case STRING:
                return cell.getStringCellValue().trim();
            case NUMERIC:
                return String.valueOf((long) cell.getNumericCellValue());
            case BOOLEAN:
                return String.valueOf(cell.getBooleanCellValue());
            case FORMULA:
                try {
                    return String.valueOf((long) cell.getNumericCellValue());
                } catch (Exception e) {
                    return cell.getStringCellValue().trim();
                }
            default:
                return "";
        }
    }

    // ==================== 基于ExcelConfig的新方法 ====================

    /**
     * 基于ExcelConfig导出Excel（流式处理）
     *
     * @param dataList 要导出的数据列表
     * @param config Excel配置
     * @return Excel文件字节数组
     */
    public <T> byte[] exportExcel(List<T> dataList, ExcelConfig<T> config) throws IOException {
        log.info("开始基于ExcelConfig的流式导出，数据行数: {}, 配置: {}", dataList.size(), config.getSheetName());

        // 将ExcelConfig数据转换为Map格式，然后使用统一的Map-based方法
        Map<String, String> headers = new LinkedHashMap<>();
        String[] headerArray = config.getFullHeaders();

        // 使用索引作为字段名
        for (int i = 0; i < headerArray.length; i++) {
            headers.put("field_" + i, headerArray[i]);
        }

        // 将数据转换为Map格式
        List<Map<String, Object>> mapData = new ArrayList<>();
        for (int i = 0; i < dataList.size(); i++) {
            T entity = dataList.get(i);
            Object[] values = config.getFullDataRow(entity, i + 1);

            Map<String, Object> rowMap = new LinkedHashMap<>();
            for (int j = 0; j < values.length; j++) {
                rowMap.put("field_" + j, values[j]);
            }
            mapData.add(rowMap);
        }

        // 使用统一的Map-based方法
        return exportGradesToExcel(mapData, headers, config.getSheetName());
    }

    /**
     * 基于ExcelConfig导入Excel（流式处理）
     *
     * @param file 上传的Excel文件
     * @param config Excel配置
     * @return 导入结果
     */
    public <T> ImportResult<T> importExcel(MultipartFile file, ExcelConfig<T> config) throws IOException {
        log.info("开始基于ExcelConfig的流式导入，文件名: {}, 配置: {}", file.getOriginalFilename(), config.getSheetName());

        ImportResult<T> result = new ImportResult<>();

        // 验证文件
        if (file == null || file.isEmpty()) {
            result.setSuccess(false);
            result.setMessage("文件不能为空");
            return result;
        }

        if (!ExcelUtils.isValidExcelFile(file.getOriginalFilename())) {
            result.setSuccess(false);
            result.setMessage("文件格式不正确，请上传Excel文件(.xlsx或.xls)");
            return result;
        }

        try (InputStream inputStream = file.getInputStream();
             Workbook workbook = WorkbookFactory.create(inputStream)) {

            Sheet sheet = workbook.getSheetAt(0);

            // 验证表头
            Row headerRow = sheet.getRow(0);
            if (headerRow == null) {
                result.setSuccess(false);
                result.setMessage("Excel文件格式错误：缺少表头");
                return result;
            }

            String[] expectedHeaders = config.getFullHeaders();
            if (!validateHeadersFromArray(headerRow, expectedHeaders)) {
                result.setSuccess(false);
                result.setMessage("Excel文件表头格式不正确，请使用正确的模板");
                return result;
            }

            // 读取数据
            List<T> successList = new ArrayList<>();
            List<ImportError> errorList = new ArrayList<>();

            int lastRowNum = sheet.getLastRowNum();
            for (int i = 1; i <= lastRowNum; i++) {
                Row row = sheet.getRow(i);
                if (row == null || ExcelUtils.isEmptyRow(row)) {
                    continue;
                }

                try {
                    Object[] rowData = extractRowDataFromArray(row, expectedHeaders.length);
                    Object[] entityData = config.extractEntityData(rowData);
                    T entity = config.getDataBuilder().apply(entityData);

                    // 验证数据
                    if (config.getValidator() != null) {
                        ExcelConfig.ValidationResult validation = config.getValidator().apply(entity);
                        if (!validation.isValid()) {
                            errorList.add(new ImportError(i + 1, validation.getErrorMessage()));
                            continue;
                        }
                    }

                    successList.add(entity);
                } catch (Exception e) {
                    log.warn("解析第{}行数据失败: {}", i + 1, e.getMessage());
                    errorList.add(new ImportError(i + 1, "数据解析失败: " + e.getMessage()));
                }
            }

            result.setSuccess(true);
            result.setSuccessCount(successList.size());
            result.setErrorCount(errorList.size());
            result.setSuccessData(successList);
            result.setErrors(errorList);
            result.setMessage(String.format("导入完成：成功%d条，失败%d条",
                                           successList.size(), errorList.size()));

            log.info("基于ExcelConfig的导入完成，成功: {}, 失败: {}", successList.size(), errorList.size());
            return result;
        }
    }

    /**
     * 基于ExcelConfig生成Excel模板
     *
     * @param config Excel配置
     * @return Excel模板的字节数组
     */
    public <T> byte[] generateTemplate(ExcelConfig<T> config) throws IOException {
        log.info("基于ExcelConfig生成Excel模板，配置: {}", config.getSheetName());

        // 将ExcelConfig转换为Map格式，然后使用统一的Map-based方法
        Map<String, String> headers = new LinkedHashMap<>();
        String[] headerArray = config.getFullHeaders();

        // 使用索引作为字段名
        for (int i = 0; i < headerArray.length; i++) {
            headers.put("field_" + i, headerArray[i]);
        }

        // 使用统一的Map-based模板生成方法
        return generateTemplate(headers, config.getSheetName());
    }

    // ==================== 辅助方法 ====================

    /**
     * 从数组创建表头行
     */
    private void createHeaderRowFromArray(Sheet sheet, String[] headers, CellStyle headerStyle) {
        Row headerRow = sheet.createRow(0);

        // 计算表头内容的最大行数，用于自适应行高
        int maxLines = 1;
        for (String header : headers) {
            if (header != null) {
                int lines = header.split("\n").length;
                maxLines = Math.max(maxLines, lines);
            }
        }

        // 设置合理的表头行高 - 适配10号字体
        // 调整行高计算，使表头更紧凑：每行约280单位 + 基础高度200
        short rowHeight = (short) (maxLines * 280 + 200);
        // 设置最大行高限制
        if (rowHeight > 800) {
            rowHeight = 800;
        }
        headerRow.setHeight(rowHeight);

        for (int i = 0; i < headers.length; i++) {
            Cell cell = headerRow.createCell(i);
            cell.setCellValue(headers[i]);
            cell.setCellStyle(headerStyle);
        }
    }



    /**
     * 验证表头（基于数组）
     */
    private boolean validateHeadersFromArray(Row headerRow, String[] expectedHeaders) {
        if (headerRow == null) {
            return false;
        }

        List<String> actualHeaders = new ArrayList<>();
        for (Cell cell : headerRow) {
            if (cell != null) {
                actualHeaders.add(getCellStringValue(cell));
            }
        }

        // 检查必需的表头是否存在
        for (String expectedHeader : expectedHeaders) {
            if (!actualHeaders.contains(expectedHeader)) {
                log.warn("缺少必需的表头: {}", expectedHeader);
                return false;
            }
        }

        return true;
    }

    /**
     * 从行中提取数据（基于数组）
     */
    private Object[] extractRowDataFromArray(Row row, int expectedLength) {
        Object[] rowData = new Object[expectedLength];

        for (int i = 0; i < expectedLength; i++) {
            Cell cell = row.getCell(i);
            rowData[i] = getCellValue(cell);
        }

        return rowData;
    }
}
