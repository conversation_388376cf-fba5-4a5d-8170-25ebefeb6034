package com.example.common.excel;

import com.example.entity.score.Grade;
import com.example.entity.score.QualityEvaluation;
import com.example.entity.student.Students;
import com.example.entity.teacher.Teacher;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * Excel配置工厂
 * 统一管理所有实体的Excel导入导出配置
 *
 * <AUTHOR>
 * @since 2025-01-31
 */
public class ExcelConfigFactory {

    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");

    /**
     * 获取成绩导入Excel配置
     */
    public static ExcelConfig<Grade> getGradeImportConfig() {
        String[] headers = {"学号", "学生姓名", "班级代码", "班级名称", "课程代码", "课程名称", "学期", "期末成绩", "是否重修", "备注"};

        return ExcelConfig.withValidation(
            headers,
            "成绩导入",
            "成绩录入模板",
            Grade.class,
            // 数据提取器（用于模板生成和导出）
            grade -> new Object[]{
                grade.getStudentId(),
                "", // 学生姓名在导入时会填充
                grade.getClassCode(),
                "", // 班级名称在导入时会填充
                grade.getCourseCode(),
                "", // 课程名称在导入时会填充
                "", // 学期名称在导入时会填充
                grade.getFinalScore(),
                grade.getIsRetake() != null && grade.getIsRetake() ? "是" : "否",
                grade.getRemarks()
            },
            // 数据构建器（用于导入）
            rowData -> {
                Grade grade = new Grade();
                grade.setStudentId(getString(rowData[0]));
                // 学生姓名不需要设置到Grade实体中
                grade.setClassCode(getString(rowData[2]));
                // 班级名称不需要设置到Grade实体中
                grade.setCourseCode(getString(rowData[4]));
                // 课程名称不需要设置到Grade实体中
                // 学期名称不需要设置到Grade实体中，semesterId在导入时会单独处理
                grade.setFinalScore(getBigDecimal(rowData[7]));
                grade.setIsRetake("是".equals(getString(rowData[8])));
                grade.setRemarks(getString(rowData[9]));
                return grade;
            },
            // 验证器
            grade -> {
                if (!StringUtils.hasText(grade.getStudentId())) {
                    return ExcelConfig.ValidationResult.error("学号不能为空");
                }
                if (!StringUtils.hasText(grade.getClassCode())) {
                    return ExcelConfig.ValidationResult.error("班级代码不能为空");
                }
                if (!StringUtils.hasText(grade.getCourseCode())) {
                    return ExcelConfig.ValidationResult.error("课程代码不能为空");
                }
                // 允许期末成绩为空，但如果不为空则需要在有效范围内
                if (grade.getFinalScore() != null) {
                    if (grade.getFinalScore().compareTo(BigDecimal.ZERO) < 0 ||
                        grade.getFinalScore().compareTo(new BigDecimal("100")) > 0) {
                        return ExcelConfig.ValidationResult.error("期末成绩必须在0-100之间");
                    }
                }
                return ExcelConfig.ValidationResult.success();
            }
        );
    }

    /**
     * 获取成绩导出Excel配置（用于成绩导出）
     */
    public static ExcelConfig<Object> getGradeExportConfig() {
        String[] headers = {"学号", "学生姓名", "班级", "课程名称", "期末成绩", "绩点", "是否重修", "学期", "备注"};

        return ExcelConfig.simple(
            headers,
            "成绩导出",
            "成绩数据",
            Object.class,
            // 数据提取器（用于Map数据）
            data -> {
                if (data instanceof java.util.Map) {
                    @SuppressWarnings("unchecked")
                    java.util.Map<String, Object> map = (java.util.Map<String, Object>) data;
                    return new Object[]{
                        map.get("studentId"),
                        map.get("studentName"),
                        map.get("className"),
                        map.get("courseName"),
                        map.get("finalScore"),
                        map.get("gradePoint"),
                        Boolean.TRUE.equals(map.get("isRetake")) ? "是" : "否",
                        map.get("semesterName"),
                        map.get("remarks")
                    };
                }
                return new Object[0];
            },
            // 数据构建器（导出不需要）
            rowData -> null
        );
    }

    /**
     * 获取学生Excel配置
     */
    public static ExcelConfig<Students> getStudentConfig() {
        String[] headers = {"学号", "姓名", "性别", "出生日期", "身份证号", "电话号码", "邮箱", "班级代码", "入学日期", "学籍状态"};

        return ExcelConfig.withValidation(
            headers,
            "学生信息",
            "学生信息模板",
            Students.class,
            // 数据提取器
            student -> new Object[]{
                student.getStudentId(),
                student.getName(),
                student.getGender(),
                student.getBirthDate() != null ? student.getBirthDate().format(DATE_FORMATTER) : "",
                student.getIdCard(),
                student.getPhone(),
                student.getEmail(),
                student.getClassCode(),
                student.getEnrollmentDate() != null ? student.getEnrollmentDate().format(DATE_FORMATTER) : "",
                student.getStatus()
            },
            // 数据构建器
            rowData -> {
                Students student = new Students();
                student.setStudentId(getString(rowData[0]));
                student.setName(getString(rowData[1]));
                student.setGender(getString(rowData[2]));
                student.setBirthDate(getLocalDate(rowData[3]));
                student.setIdCard(getString(rowData[4]));
                student.setPhone(getString(rowData[5]));
                student.setEmail(getString(rowData[6]));
                student.setClassCode(getString(rowData[7]));
                student.setEnrollmentDate(getLocalDate(rowData[8]));
                student.setStatus(getString(rowData[9]));
                return student;
            },
            // 验证器
            student -> {
                if (!StringUtils.hasText(student.getStudentId())) {
                    return ExcelConfig.ValidationResult.error("学号不能为空");
                }
                if (!StringUtils.hasText(student.getName())) {
                    return ExcelConfig.ValidationResult.error("姓名不能为空");
                }
                if (!StringUtils.hasText(student.getClassCode())) {
                    return ExcelConfig.ValidationResult.error("班级代码不能为空");
                }
                return ExcelConfig.ValidationResult.success();
            }
        );
    }

    /**
     * 获取教师Excel配置
     */
    public static ExcelConfig<Teacher> getTeacherConfig() {
        String[] headers = {"教师编号", "姓名", "性别", "出生日期", "电话号码", "邮箱", "学院代码", "职称", "状态"};

        return ExcelConfig.withValidation(
            headers,
            "教师信息",
            "教师信息模板",
            Teacher.class,
            // 数据提取器
            teacher -> new Object[]{
                teacher.getTeacherCode(),
                teacher.getName(),
                teacher.getGender(),
                teacher.getBirthDate() != null ? teacher.getBirthDate().format(DATE_FORMATTER) : "",
                teacher.getPhone(),
                teacher.getEmail(),
                teacher.getCollegeCode(),
                teacher.getTitle(),
                teacher.getStatus()
            },
            // 数据构建器
            rowData -> {
                Teacher teacher = new Teacher();
                teacher.setTeacherCode(getString(rowData[0]));
                teacher.setName(getString(rowData[1]));
                teacher.setGender(getString(rowData[2]));
                teacher.setBirthDate(getLocalDate(rowData[3]));
                teacher.setPhone(getString(rowData[4]));
                teacher.setEmail(getString(rowData[5]));
                teacher.setCollegeCode(getString(rowData[6]));
                teacher.setTitle(getString(rowData[7]));
                teacher.setStatus(getString(rowData[8]));
                return teacher;
            },
            // 验证器
            teacher -> {
                if (!StringUtils.hasText(teacher.getTeacherCode())) {
                    return ExcelConfig.ValidationResult.error("教师编号不能为空");
                }
                if (!StringUtils.hasText(teacher.getName())) {
                    return ExcelConfig.ValidationResult.error("姓名不能为空");
                }
                return ExcelConfig.ValidationResult.success();
            }
        );
    }

    /**
     * 获取基本素质测评导入Excel配置
     */
    public static ExcelConfig<QualityEvaluation> getQualityEvaluationImportConfig() {
        String[] headers = {"学号", "学期ID", "加分说明", "扣分说明"};

        return ExcelConfig.withValidation(
            headers,
            "基本素质测评导入",
            "基本素质测评导入模板",
            QualityEvaluation.class,
            // 数据提取器（用于模板生成）
            evaluation -> new Object[]{
                evaluation.getStudentId(),
                evaluation.getSemesterId(),
                evaluation.getAddScoreRemark(),
                evaluation.getReduceScoreRemark()
            },
            // 数据构建器（用于导入）
            rowData -> {
                QualityEvaluation evaluation = new QualityEvaluation();
                evaluation.setStudentId(getString(rowData[0]));
                evaluation.setSemesterId(getInteger(rowData[1]));
                evaluation.setAddScoreRemark(getString(rowData[2]));
                evaluation.setReduceScoreRemark(getString(rowData[3]));

                // 设置默认值
                evaluation.setAddScore(new BigDecimal("0"));
                evaluation.setReduceScore(new BigDecimal("0"));
                evaluation.setPeriodScore(new BigDecimal("80")); // 默认周期得分80分
                evaluation.setTotalScore(new BigDecimal("80"));
                evaluation.setCreateTime(LocalDateTime.now());
                evaluation.setUpdateTime(LocalDateTime.now());

                return evaluation;
            },
            // 验证器
            evaluation -> {
                if (!StringUtils.hasText(evaluation.getStudentId())) {
                    return ExcelConfig.ValidationResult.error("学号不能为空");
                }
                if (evaluation.getSemesterId() == null) {
                    return ExcelConfig.ValidationResult.error("学期ID不能为空");
                }
                return ExcelConfig.ValidationResult.success();
            }
        );
    }

    /**
     * 获取基本素质测评导出Excel配置
     */
    public static ExcelConfig<Object> getQualityEvaluationExportConfig() {
        String[] headers = {"序号", "学号", "姓名", "宿舍号", "学期名称", "周期得分", "加分说明", "扣分说明",
                          "加分", "扣分", "周期得分", "总分"};

        return ExcelConfig.simple(
            headers,
            "基本素质测评导出",
            "基本素质测评数据",
            Object.class,
            // 数据提取器（用于Map数据）
            data -> {
                if (data instanceof java.util.Map) {
                    @SuppressWarnings("unchecked")
                    java.util.Map<String, Object> map = (java.util.Map<String, Object>) data;
                    return new Object[]{
                        map.get("rowNumber"),        // 序号
                        map.get("studentId"),        // 学号
                        map.get("studentName"),      // 姓名
                        map.get("dormitory"),        // 宿舍号
                        map.get("semesterName"),     // 学期名称
                        map.get("periodScore"),      // 周期得分
                        map.get("addScoreRemark"),   // 加分说明
                        map.get("reduceScoreRemark"), // 扣分说明
                        map.get("addScore"),         // 加分
                        map.get("reduceScore"),      // 扣分
                        map.get("periodScore"),      // 周期得分（重复显示）
                        map.get("totalScore")        // 总分
                    };
                }
                return new Object[0];
            },
            // 数据构建器（导出不需要）
            rowData -> null
        );
    }

    // 工具方法
    private static String getString(Object value) {
        return value != null ? value.toString().trim() : "";
    }

    private static Integer getInteger(Object value) {
        if (value == null || !StringUtils.hasText(value.toString())) {
            return null;
        }
        try {
            return Integer.valueOf(value.toString().trim());
        } catch (NumberFormatException e) {
            return null;
        }
    }

    private static BigDecimal getBigDecimal(Object value) {
        if (value == null || !StringUtils.hasText(value.toString())) {
            return null;
        }
        try {
            return new BigDecimal(value.toString().trim());
        } catch (NumberFormatException e) {
            return null;
        }
    }

    private static LocalDate getLocalDate(Object value) {
        if (value == null || !StringUtils.hasText(value.toString())) {
            return null;
        }
        try {
            return LocalDate.parse(value.toString().trim(), DATE_FORMATTER);
        } catch (Exception e) {
            return null;
        }
    }

    private static LocalDateTime getLocalDateTime(Object value) {
        if (value == null || !StringUtils.hasText(value.toString())) {
            return null;
        }
        try {
            return LocalDateTime.parse(value.toString().trim(), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        } catch (Exception e) {
            return null;
        }
    }
}
