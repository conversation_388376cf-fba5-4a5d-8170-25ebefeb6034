package com.example.controller.basic;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.example.common.Result;
import com.example.dto.basic.ClassesQueryDTO;
import com.example.service.basic.ClassesService;
import com.example.vo.basic.ClassesVO;
import com.example.vo.educational.CourseVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 班级管理控制器
 *
 * <AUTHOR>
 * @since 2025-07-27
 */
@RestController
@RequestMapping("/api/basic/classes")
@RequiredArgsConstructor
@Tag(name = "班级管理", description = "班级管理相关接口")
public class ClassesController {

    private final ClassesService classesService;

    /**
     * 分页查询班级列表
     */
    @PostMapping("/page")
    @Operation(summary = "分页查询班级列表", description = "根据条件分页查询班级列表")
    public Result<IPage<ClassesVO>> getClassesPage(@RequestBody ClassesQueryDTO query) {
        try {
            IPage<ClassesVO> page = classesService.getClassesPage(query);
            return Result.success("查询成功", page);
        } catch (Exception e) {
            return Result.error("查询班级列表失败");
        }
    }

    /**
     * 根据ID查询班级详情
     */
    @GetMapping("/{id}")
    @Operation(summary = "查询班级详情", description = "根据ID查询班级详情")
    public Result<ClassesVO> getClassesById(@PathVariable Integer id) {
        try {
            ClassesVO classesVO = classesService.getClassesById(id);
            return Result.success("查询成功", classesVO);
        } catch (Exception e) {
            return Result.error("查询班级详情失败");
        }
    }

    /**
     * 查询所有班级列表（用于下拉选择）
     */
    @GetMapping("/all")
    @Operation(summary = "查询所有班级", description = "查询所有班级列表，用于下拉选择")
    public Result<List<ClassesVO>> getAllClasses() {
        try {
            List<ClassesVO> list = classesService.getAllClasses();
            return Result.success("查询成功", list);
        } catch (Exception e) {
            return Result.error("查询所有班级失败");
        }
    }

    /**
     * 根据专业代码查询班级列表
     */
    @GetMapping("/by-major/{majorCode}")
    @Operation(summary = "根据专业查询班级", description = "根据专业代码查询班级列表")
    public Result<List<ClassesVO>> getClassesByMajorCode(@PathVariable String majorCode) {
        try {
            List<ClassesVO> list = classesService.getClassesByMajorCode(majorCode);
            return Result.success("查询成功", list);
        } catch (Exception e) {
            return Result.error("查询班级失败");
        }
    }

    /**
     * 根据班主任工号查询班级列表
     */
    @GetMapping("/by-teacher/{headTeacherCode}")
    @Operation(summary = "根据班主任查询班级", description = "根据班主任工号查询班级列表")
    public Result<List<ClassesVO>> getClassesByHeadTeacher(@PathVariable String headTeacherCode) {
        try {
            List<ClassesVO> list = classesService.getClassesByHeadTeacher(headTeacherCode);
            return Result.success("查询成功", list);
        } catch (Exception e) {
            return Result.error("查询班级失败");
        }
    }

    /**
     * 根据班级代码查询班级信息
     */
    @GetMapping("/by-code/{classCode}")
    @Operation(summary = "根据班级代码查询班级", description = "根据班级代码查询班级信息")
    public Result<ClassesVO> getClassesByCode(@PathVariable String classCode) {
        try {
            ClassesVO classesVO = classesService.getClassesByCode(classCode);
            if (classesVO == null) {
                return Result.error("未找到该班级信息");
            }
            return Result.success("查询成功", classesVO);
        } catch (Exception e) {
            return Result.error("查询班级失败");
        }
    }

    /**
     * 新增班级
     */
    @PostMapping
    @Operation(summary = "新增班级", description = "新增班级信息")
    public Result<Void> saveClasses(@Validated @RequestBody ClassesVO classesVO) {
        try {
            classesService.saveClasses(classesVO);
            return Result.success("新增班级成功");
        } catch (Exception e) {
            return Result.error("新增班级失败: " + e.getMessage());
        }
    }

    /**
     * 更新班级
     */
    @PutMapping
    @Operation(summary = "更新班级", description = "更新班级信息")
    public Result<Void> updateClasses(@Validated @RequestBody ClassesVO classesVO) {
        try {
            classesService.updateClasses(classesVO);
            return Result.success("更新班级成功");
        } catch (Exception e) {
            return Result.error("更新班级失败: " + e.getMessage());
        }
    }

    /**
     * 删除班级
     */
    @DeleteMapping("/{id}")
    @Operation(summary = "删除班级", description = "根据ID删除班级")
    public Result<Void> deleteClasses(@PathVariable Integer id) {
        try {
            classesService.deleteClasses(id);
            return Result.success("删除班级成功");
        } catch (Exception e) {
            return Result.error("删除班级失败: " + e.getMessage());
        }
    }

    /**
     * 批量删除班级
     */
    @DeleteMapping("/batch")
    @Operation(summary = "批量删除班级", description = "批量删除班级")
    public Result<Void> batchDeleteClasses(@RequestBody List<Integer> ids) {
        try {
            classesService.batchDeleteClasses(ids);
            return Result.success("批量删除班级成功");
        } catch (Exception e) {
            return Result.error("批量删除班级失败: " + e.getMessage());
        }
    }



    /**
     * 更新班级学生人数
     */
    @PutMapping("/student-count")
    @Operation(summary = "更新学生人数", description = "更新班级学生人数")
    public Result<Void> updateStudentCount(@RequestBody Map<String, Object> params) {
        try {
            Integer classId = Integer.valueOf(params.get("classId").toString());
            Integer studentCount = Integer.valueOf(params.get("studentCount").toString());

            classesService.updateStudentCount(classId, studentCount);
            return Result.success("更新学生人数成功");
        } catch (Exception e) {
            return Result.error("更新学生人数失败: " + e.getMessage());
        }
    }

    /**
     * 获取班级在指定学期的课程列表
     */
    @GetMapping("/{classCode}/courses/{semesterId}")
    @Operation(summary = "获取班级课程", description = "获取指定班级在指定学期的课程列表")
    public Result<List<CourseVO>> getCoursesByClassAndSemester(
            @PathVariable String classCode,
            @PathVariable Integer semesterId) {
        try {
            List<CourseVO> courses = classesService.getCoursesByClassAndSemester(classCode, semesterId);
            return Result.success(courses);
        } catch (Exception e) {
            return Result.error("获取班级课程失败: " + e.getMessage());
        }
    }
}
