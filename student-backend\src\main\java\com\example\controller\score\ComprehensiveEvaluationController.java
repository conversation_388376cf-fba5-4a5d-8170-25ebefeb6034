package com.example.controller.score;

import com.example.common.Result;
import com.example.dto.score.ComprehensiveEvaluationQueryDTO;
import com.example.service.score.ComprehensiveEvaluationService;
import com.example.vo.score.ComprehensiveEvaluationVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.Resource;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Map;

/**
 * 综合素质测评成绩对接控制器
 * 专注于导出功能：整合学业成绩（期末成绩）和基本素质测评成绩
 *
 * <AUTHOR>
 * @since 2025-08-02
 */
@Tag(name = "综合素质测评成绩对接", description = "综合素质测评成绩导出接口")
@RestController
@RequestMapping("/api/score/comprehensive-evaluation")
@RequiredArgsConstructor
@Slf4j
@Validated
public class ComprehensiveEvaluationController {

    private final ComprehensiveEvaluationService comprehensiveEvaluationService;

    @Operation(summary = "查询综合素质测评成绩数据",
               description = "查询综合素质测评成绩数据，支持分页")
    @GetMapping("/list")
    public Result<Map<String, Object>> list(
            @Parameter(description = "学年") @RequestParam(required = false) String academicYear,
            @Parameter(description = "学生ID") @RequestParam(required = false) String studentId,
            @Parameter(description = "班级名称") @RequestParam(required = false) String className,
            @Parameter(description = "当前页") @RequestParam(defaultValue = "1") Integer currentPage,
            @Parameter(description = "页大小") @RequestParam(defaultValue = "20") Integer pageSize) {
        log.info("查询综合素质测评成绩数据，学年：{}，学生ID：{}，班级：{}，页码：{}，页大小：{}",
                academicYear, studentId, className, currentPage, pageSize);

        try {
            ComprehensiveEvaluationQueryDTO queryDTO = new ComprehensiveEvaluationQueryDTO();
            queryDTO.setAcademicYear(academicYear);
            queryDTO.setStudentId(studentId);
            queryDTO.setClassName(className);

            List<ComprehensiveEvaluationVO> allData = comprehensiveEvaluationService.getComprehensiveEvaluationList(queryDTO);

            // 分页处理
            int total = allData.size();
            int startIndex = (currentPage - 1) * pageSize;
            int endIndex = Math.min(startIndex + pageSize, total);
            List<ComprehensiveEvaluationVO> pagedData = allData.subList(startIndex, endIndex);

            // 构造分页结果
            Map<String, Object> result = new HashMap<>();
            result.put("records", pagedData);
            result.put("total", total);
            result.put("current", currentPage);
            result.put("size", pageSize);
            result.put("pages", (int) Math.ceil((double) total / pageSize));

            log.info("查询成功，总计 {} 条数据，当前页 {} 条数据", total, pagedData.size());
            return Result.success(result);
        } catch (Exception e) {
            log.error("查询综合素质测评成绩数据失败", e);
            return Result.error("查询数据失败：" + e.getMessage());
        }
    }

    @Operation(summary = "导出综合素质测评成绩Excel",
               description = "整合学业成绩（期末成绩）和基本素质测评成绩，按70%:30%计算综合成绩")
    @GetMapping("/export")
    public ResponseEntity<Resource> exportComprehensiveEvaluation(
            @Parameter(description = "学年") @RequestParam(required = false) String academicYear,
            @Parameter(description = "班级名称") @RequestParam(required = false) String className,
            @Parameter(description = "学生ID") @RequestParam(required = false) String studentId) {
        try {
            log.info("开始导出综合素质测评成绩: 学年={}, 班级={}, 学生ID={}",
                    academicYear, className, studentId);

            ComprehensiveEvaluationQueryDTO queryDTO = new ComprehensiveEvaluationQueryDTO();
            queryDTO.setAcademicYear(academicYear);
            queryDTO.setClassName(className);
            queryDTO.setStudentId(studentId);

            byte[] excelData = comprehensiveEvaluationService.exportComprehensiveEvaluation(queryDTO);

            // 生成文件名
            String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
            String fileName = String.format("综合素质测评成绩_%s_%s.xlsx",
                    academicYear != null ? academicYear : "全部", timestamp);

            log.info("综合素质测评成绩导出成功，文件大小: {} bytes", excelData.length);

            return ResponseEntity.ok()
                    .header("Content-Disposition", "attachment; filename=\"" + fileName + "\"")
                    .header("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
                    .body(new org.springframework.core.io.ByteArrayResource(excelData));
        } catch (Exception e) {
            log.error("导出综合素质测评成绩失败", e);
            throw new RuntimeException("导出失败: " + e.getMessage());
        }
    }

    @Operation(summary = "获取综合素质测评成绩预览数据",
               description = "用于前端预览，返回少量数据用于展示")
    @GetMapping("/preview")
    public Result<List<ComprehensiveEvaluationVO>> getComprehensiveEvaluationPreview(
            @Parameter(description = "学年") @RequestParam(required = false) String academicYear,
            @Parameter(description = "班级名称") @RequestParam(required = false) String className,
            @Parameter(description = "限制条数") @RequestParam(defaultValue = "10") Integer limit) {
        try {
            ComprehensiveEvaluationQueryDTO queryDTO = new ComprehensiveEvaluationQueryDTO();
            queryDTO.setAcademicYear(academicYear);
            queryDTO.setClassName(className);
            queryDTO.setLimit(limit);

            List<ComprehensiveEvaluationVO> result = comprehensiveEvaluationService.getComprehensiveByAcademicYear(queryDTO);
            return Result.success("查询成功", result);
        } catch (Exception e) {
            log.error("获取综合素质测评成绩预览失败", e);
            return Result.error("获取预览数据失败: " + e.getMessage());
        }
    }
}
