package com.example.controller.score;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.example.common.Result;
import com.example.common.excel.ApachePoiExportService;
import com.example.common.excel.ExcelConfig;
import com.example.common.excel.ExcelConfigFactory;
import com.example.dto.score.QualityEvaluationQueryDTO;
import com.example.entity.score.QualityEvaluation;
import com.example.service.score.QualityEvaluationService;
import com.example.vo.score.QualityEvaluationVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 基本素质测评Controller
 *
 * <AUTHOR>
 * @since 2025-08-01
 */
@Slf4j
@RestController
@RequestMapping("/api/score/quality-evaluation")
@RequiredArgsConstructor
@Tag(name = "基本素质测评管理", description = "基本素质测评相关接口")
public class QualityEvaluationController {

    private final QualityEvaluationService qualityEvaluationService;
    private final ApachePoiExportService apachePoiExportService;

    /**
     * 分页查询基本素质测评列表
     */
    @PostMapping("/page")
    @Operation(summary = "分页查询基本素质测评列表", description = "根据条件分页查询基本素质测评信息")
    public Result<IPage<QualityEvaluationVO>> getQualityEvaluationPage(@RequestBody QualityEvaluationQueryDTO queryDTO) {
        try {
            IPage<QualityEvaluationVO> result = qualityEvaluationService.getQualityEvaluationPage(queryDTO);
            return Result.success("查询成功", result);
        } catch (Exception e) {
            log.error("查询基本素质测评列表失败", e);
            return Result.error("查询基本素质测评列表失败");
        }
    }

    /**
     * 根据ID查询基本素质测评详情
     */
    @GetMapping("/{evaluationId}")
    @Operation(summary = "查询基本素质测评详情", description = "根据ID查询基本素质测评详细信息")
    public Result<QualityEvaluationVO> getQualityEvaluationById(@PathVariable Integer evaluationId) {
        try {
            QualityEvaluationVO result = qualityEvaluationService.getQualityEvaluationById(evaluationId);
            if (result == null) {
                return Result.error("基本素质测评记录不存在");
            }
            return Result.success("查询成功", result);
        } catch (Exception e) {
            log.error("查询基本素质测评详情失败", e);
            return Result.error("查询基本素质测评详情失败");
        }
    }

    /**
     * 新增基本素质测评
     */
    @PostMapping
    @Operation(summary = "新增基本素质测评", description = "新增基本素质测评记录")
    public Result<Void> saveQualityEvaluation(@Validated @RequestBody QualityEvaluation qualityEvaluation) {
        try {
            boolean result = qualityEvaluationService.saveQualityEvaluation(qualityEvaluation);
            if (result) {
                return Result.success("新增成功");
            } else {
                return Result.error("新增失败");
            }
        } catch (Exception e) {
            log.error("新增基本素质测评失败", e);
            return Result.error("新增失败：" + e.getMessage());
        }
    }

    /**
     * 更新基本素质测评
     */
    @PutMapping
    @Operation(summary = "更新基本素质测评", description = "更新基本素质测评记录")
    public Result<Void> updateQualityEvaluation(@Validated @RequestBody QualityEvaluation qualityEvaluation) {
        try {
            if (qualityEvaluation.getEvaluationId() == null) {
                return Result.error("评估ID不能为空");
            }
            boolean result = qualityEvaluationService.updateQualityEvaluation(qualityEvaluation);
            if (result) {
                return Result.success("更新成功");
            } else {
                return Result.error("更新失败");
            }
        } catch (Exception e) {
            log.error("更新基本素质测评失败", e);
            return Result.error("更新失败：" + e.getMessage());
        }
    }

    /**
     * 删除基本素质测评
     */
    @DeleteMapping("/{evaluationId}")
    @Operation(summary = "删除基本素质测评", description = "根据ID删除基本素质测评记录")
    public Result<Void> deleteQualityEvaluation(@PathVariable Integer evaluationId) {
        try {
            boolean result = qualityEvaluationService.deleteQualityEvaluation(evaluationId);
            if (result) {
                return Result.success("删除成功");
            } else {
                return Result.error("删除失败");
            }
        } catch (Exception e) {
            log.error("删除基本素质测评失败", e);
            return Result.error("删除失败：" + e.getMessage());
        }
    }

    /**
     * 批量删除基本素质测评
     */
    @PostMapping("/batch-delete")
    @Operation(summary = "批量删除基本素质测评", description = "批量删除基本素质测评记录")
    public Result<Void> batchDeleteQualityEvaluation(@RequestBody List<Integer> evaluationIds) {
        try {
            if (evaluationIds == null || evaluationIds.isEmpty()) {
                return Result.error("请选择要删除的记录");
            }
            boolean result = qualityEvaluationService.batchDeleteQualityEvaluation(evaluationIds);
            if (result) {
                return Result.success("批量删除成功");
            } else {
                return Result.error("批量删除失败");
            }
        } catch (Exception e) {
            log.error("批量删除基本素质测评失败", e);
            return Result.error("批量删除失败：" + e.getMessage());
        }
    }



    /**
     * 根据学号和学期查询基本素质测评
     */
    @GetMapping("/by-student/{studentId}/{semesterId}")
    @Operation(summary = "根据学号和学期查询", description = "根据学号和学期查询基本素质测评记录")
    public Result<QualityEvaluation> getByStudentIdAndSemester(
            @PathVariable String studentId,
            @PathVariable Integer semesterId) {
        try {
            QualityEvaluation result = qualityEvaluationService.getByStudentIdAndSemester(studentId, semesterId);
            return Result.success("查询成功", result);
        } catch (Exception e) {
            log.error("根据学号和学期查询基本素质测评失败", e);
            return Result.error("查询失败");
        }
    }

    /**
     * 导出基本素质测评成绩
     */
    @GetMapping("/export")
    @Operation(summary = "导出基本素质测评成绩", description = "根据查询条件导出基本素质测评成绩到Excel")
    public ResponseEntity<byte[]> exportExcel(QualityEvaluationQueryDTO queryDTO) {
        try {
            // 查询数据
            if (queryDTO == null) {
                queryDTO = new QualityEvaluationQueryDTO();
            }
            queryDTO.setCurrent(1);
            queryDTO.setSize(10000); // 最多导出10000条数据

            IPage<QualityEvaluationVO> result = qualityEvaluationService.getQualityEvaluationPage(queryDTO);
            List<QualityEvaluationVO> dataList = result.getRecords();

            // 转换为Map格式，用于Excel导出
            List<Map<String, Object>> exportData = new ArrayList<>();
            for (int i = 0; i < dataList.size(); i++) {
                QualityEvaluationVO vo = dataList.get(i);
                Map<String, Object> map = new HashMap<>();
                map.put("rowNumber", i + 1);  // 序号，从1开始
                map.put("studentId", vo.getStudentId());
                map.put("studentName", vo.getStudentName());
                map.put("dormitory", vo.getDormitory());
                map.put("semesterName", vo.getSemesterName());
                map.put("periodScore", vo.getPeriodScore());
                map.put("addScoreRemark", vo.getAddScoreRemark() != null ? vo.getAddScoreRemark() : "");
                map.put("reduceScoreRemark", vo.getReduceScoreRemark() != null ? vo.getReduceScoreRemark() : "");
                map.put("addScore", vo.getAddScore());
                map.put("reduceScore", vo.getReduceScore());
                map.put("totalScore", vo.getTotalScore());
                exportData.add(map);
            }

            // 构建表头映射（与期末成绩导出保持一致的样式）
            Map<String, String> excelHeaders = new LinkedHashMap<>();
            excelHeaders.put("rowNumber", "序号");
            excelHeaders.put("studentId", "学号");
            excelHeaders.put("studentName", "姓名");
            excelHeaders.put("dormitory", "宿舍号");
            excelHeaders.put("semesterName", "学期名称");
            excelHeaders.put("periodScore", "周期得分");
            excelHeaders.put("addScoreRemark", "加分说明");
            excelHeaders.put("reduceScoreRemark", "扣分说明");
            excelHeaders.put("addScore", "加分");
            excelHeaders.put("reduceScore", "扣分");
            excelHeaders.put("periodScoreFinal", "周期得分");
            excelHeaders.put("totalScore", "总分");

            // 为了显示两次周期得分，需要复制一份数据
            for (Map<String, Object> map : exportData) {
                map.put("periodScoreFinal", map.get("periodScore"));
            }

            // 使用与期末成绩相同的导出方法，保持样式一致
            String sheetName = "基本素质测评成绩";
            byte[] excelData = apachePoiExportService.exportGradesToExcel(exportData, excelHeaders, sheetName);

            // 生成文件名
            String fileName = "基本素质测评成绩_" + new SimpleDateFormat("yyyyMMddHHmmss").format(new Date()) + ".xlsx";
            String encodedFileName = URLEncoder.encode(fileName, StandardCharsets.UTF_8);

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
            headers.setContentDispositionFormData("attachment", encodedFileName);

            log.info("导出基本素质测评成绩成功，共{}条数据", dataList.size());
            return ResponseEntity.ok()
                    .headers(headers)
                    .body(excelData);
        } catch (Exception e) {
            log.error("导出基本素质测评成绩失败", e);
            throw new RuntimeException("导出失败: " + e.getMessage());
        }
    }

    /**
     * 下载导入模板
     */
    @GetMapping("/template")
    @Operation(summary = "下载导入模板", description = "下载基本素质测评导入模板")
    public ResponseEntity<byte[]> downloadTemplate() {
        try {
            // 使用Excel工具类生成模板
            byte[] templateData = apachePoiExportService.generateTemplate(
                ExcelConfigFactory.getQualityEvaluationImportConfig()
            );

            // 生成文件名
            String fileName = "基本素质测评导入模板_" + new SimpleDateFormat("yyyyMMddHHmmss").format(new Date()) + ".xlsx";
            String encodedFileName = URLEncoder.encode(fileName, StandardCharsets.UTF_8);

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
            headers.setContentDispositionFormData("attachment", encodedFileName);

            log.info("下载基本素质测评导入模板成功");
            return ResponseEntity.ok()
                    .headers(headers)
                    .body(templateData);
        } catch (Exception e) {
            log.error("下载基本素质测评导入模板失败", e);
            throw new RuntimeException("下载模板失败: " + e.getMessage());
        }
    }

    /**
     * 导入基本素质测评成绩
     */
    @PostMapping("/import")
    @Operation(summary = "导入基本素质测评成绩", description = "从Excel文件导入基本素质测评成绩")
    public Result<String> importExcel(@RequestParam("file") MultipartFile file) {
        long startTime = System.currentTimeMillis();
        log.info("开始导入基本素质测评数据，文件名: {}, 大小: {} bytes",
                file.getOriginalFilename(), file.getSize());

        try {
            // 使用Excel工具类导入
            ApachePoiExportService.ImportResult<QualityEvaluation> importResult =
                apachePoiExportService.importExcel(file, ExcelConfigFactory.getQualityEvaluationImportConfig());

            if (!importResult.isSuccess()) {
                return Result.error(importResult.getMessage());
            }

            List<QualityEvaluation> successData = importResult.getSuccessData();
            if (successData.isEmpty()) {
                return Result.error("没有有效的数据可导入，请检查Excel文件格式和内容");
            }

            // 逐条处理，支持新增和更新
            int insertCount = 0;
            int updateCount = 0;
            List<String> errorMessages = new ArrayList<>();

            for (int i = 0; i < successData.size(); i++) {
                QualityEvaluation record = successData.get(i);
                try {
                    // 检查是否已存在相同学生和学期的记录
                    QualityEvaluation existing = qualityEvaluationService.getByStudentIdAndSemester(
                        record.getStudentId(), record.getSemesterId());

                    if (existing != null) {
                        // 更新现有记录
                        record.setEvaluationId(existing.getEvaluationId());
                        record.setCreateTime(existing.getCreateTime()); // 保持原创建时间
                        qualityEvaluationService.updateQualityEvaluation(record);
                        updateCount++;
                        log.info("更新记录：学生ID={}, 学期ID={}", record.getStudentId(), record.getSemesterId());
                    } else {
                        // 新增记录
                        qualityEvaluationService.saveQualityEvaluation(record);
                        insertCount++;
                        log.info("新增记录：学生ID={}, 学期ID={}", record.getStudentId(), record.getSemesterId());
                    }
                } catch (Exception e) {
                    String errorMsg = String.format("第%d条记录处理失败：%s", i + 1, e.getMessage());
                    errorMessages.add(errorMsg);
                    log.error("导入记录失败：{}", errorMsg, e);
                }
            }

            boolean saveResult = (insertCount + updateCount) > 0;

            long endTime = System.currentTimeMillis();
            String resultMessage = String.format("导入完成！新增 %d 条记录，更新 %d 条记录，失败 %d 条记录，耗时 %d 毫秒",
                    insertCount, updateCount, errorMessages.size(), (endTime - startTime));

            // 添加错误详情
            List<String> allErrors = new ArrayList<>();

            // 添加Excel解析错误
            if (importResult.getErrorCount() > 0) {
                List<ApachePoiExportService.ImportError> excelErrors = importResult.getErrors();
                allErrors.addAll(excelErrors.stream()
                    .map(error -> "第" + error.getRowNumber() + "行：" + error.getErrorMessage())
                    .collect(Collectors.toList()));
            }

            // 添加保存错误
            allErrors.addAll(errorMessages);

            if (!allErrors.isEmpty()) {
                if (allErrors.size() <= 10) {
                    resultMessage += "\n错误详情：\n" + String.join("\n", allErrors);
                } else {
                    resultMessage += "\n错误详情（仅显示前10条）：\n" +
                        String.join("\n", allErrors.subList(0, 10));
                }
            }

            log.info("导入基本素质测评数据完成：新增{}条，更新{}条，失败{}条",
                    insertCount, updateCount, errorMessages.size());

            if (saveResult) {
                return Result.success(resultMessage);
            } else {
                return Result.error("数据保存失败");
            }

        } catch (Exception e) {
            log.error("导入基本素质测评数据失败", e);
            return Result.error("导入失败: " + e.getMessage());
        }
    }
}
