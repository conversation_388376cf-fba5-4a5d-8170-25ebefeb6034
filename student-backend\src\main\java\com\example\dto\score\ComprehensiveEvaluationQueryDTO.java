package com.example.dto.score;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 综合素质测评成绩查询DTO
 *
 * <AUTHOR>
 * @since 2025-08-02
 */
@Data
@Schema(description = "综合素质测评成绩查询DTO")
public class ComprehensiveEvaluationQueryDTO {

    @Schema(description = "学年")
    private String academicYear;

    @Schema(description = "学生ID")
    private String studentId;

    @Schema(description = "学生姓名")
    private String studentName;

    @Schema(description = "班级名称")
    private String className;

    @Schema(description = "专业代码")
    private String majorCode;

    @Schema(description = "学院代码")
    private String collegeCode;

    @Schema(description = "最小学业成绩")
    private Double minAcademicScore;

    @Schema(description = "最大学业成绩")
    private Double maxAcademicScore;

    @Schema(description = "最小基本素质测评成绩")
    private Double minQualityScore;

    @Schema(description = "最大基本素质测评成绩")
    private Double maxQualityScore;

    @Schema(description = "最小综合素质测评成绩")
    private Double minComprehensiveScore;

    @Schema(description = "最大综合素质测评成绩")
    private Double maxComprehensiveScore;

    @Schema(description = "当前页码")
    private Integer currentPage = 1;

    @Schema(description = "每页大小")
    private Integer pageSize = 20;

    @Schema(description = "限制返回记录数")
    private Integer limit;
}
