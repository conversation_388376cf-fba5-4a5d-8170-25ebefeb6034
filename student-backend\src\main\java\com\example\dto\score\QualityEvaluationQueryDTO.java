package com.example.dto.score;

import com.example.dto.BaseQueryDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 基本素质测评查询参数DTO
 *
 * <AUTHOR>
 * @since 2025-08-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class QualityEvaluationQueryDTO extends BaseQueryDTO {

    /**
     * 学号
     */
    private String studentId;

    /**
     * 学生姓名
     */
    private String studentName;

    /**
     * 宿舍号
     */
    private String dormitory;

    /**
     * 班级代码
     */
    private String classCode;

    /**
     * 专业代码
     */
    private String majorCode;

    /**
     * 学院代码
     */
    private String collegeCode;

    /**
     * 学期ID
     */
    private Integer semesterId;

    /**
     * 学年
     */
    private String academicYear;

    /**
     * 学期号
     */
    private Integer semesterNumber;

    /**
     * 最小总分
     */
    private Double minTotalScore;

    /**
     * 最大总分
     */
    private Double maxTotalScore;
}
