package com.example.entity.score;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 基本素质测评实体类
 *
 * <AUTHOR>
 * @since 2025-08-01
 */
@Data
@TableName("quality_evaluation")
public class QualityEvaluation implements Serializable {

    /**
     * 主键ID
     */
    @TableId(value = "evaluation_id", type = IdType.AUTO)
    private Integer evaluationId;

    /**
     * 学号
     */
    @TableField("student_id")
    private String studentId;

    /**
     * 加分项
     */
    @TableField("add_score")
    private BigDecimal addScore;

    /**
     * 扣分项
     */
    @TableField("reduce_score")
    private BigDecimal reduceScore;

    /**
     * 加分说明
     */
    @TableField("add_score_remark")
    private String addScoreRemark;

    /**
     * 扣分说明
     */
    @TableField("reduce_score_remark")
    private String reduceScoreRemark;

    /**
     * 学期ID
     */
    @TableField("semester_id")
    private Integer semesterId;

    /**
     * 周期得分
     */
    @TableField("period_score")
    private BigDecimal periodScore;

    /**
     * 总得分
     */
    @TableField("total_score")
    private BigDecimal totalScore;

    /**
     * 上一学期总分
     */
    @TableField("prev_period_score")
    private BigDecimal prevPeriodScore;



    /**
     * 创建时间
     */
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private LocalDateTime updateTime;
}
