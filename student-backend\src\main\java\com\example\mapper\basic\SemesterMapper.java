package com.example.mapper.basic;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.example.dto.basic.SemesterQueryDTO;
import com.example.entity.basic.Semester;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 学年学期Mapper
 *
 * <AUTHOR>
 * @since 2025-07-27
 */
@Mapper
public interface SemesterMapper extends BaseMapper<Semester> {

    /**
     * 分页查询学年学期列表
     *
     * @param page 分页参数
     * @param query 查询条件
     * @return 学年学期列表
     */
    IPage<Semester> selectSemesterPage(IPage<Semester> page, @Param("query") SemesterQueryDTO query);

    /**
     * 查询所有学年学期
     *
     * @return 学年学期列表
     */
    List<Semester> selectAllSemesters();

    /**
     * 根据学年和学期号查询学期
     *
     * @param academicYear 学年
     * @param semesterNumber 学期号
     * @return 学期信息
     */
    Semester selectByAcademicYearAndNumber(@Param("academicYear") String academicYear,
                                          @Param("semesterNumber") Integer semesterNumber);

    /**
     * 检查学年学期是否存在
     *
     * @param academicYear 学年
     * @param semesterNumber 学期号
     * @param excludeId 排除的学期ID
     * @return 是否存在
     */
    Boolean existsByAcademicYearAndNumber(@Param("academicYear") String academicYear,
                                         @Param("semesterNumber") Integer semesterNumber,
                                         @Param("excludeId") Integer excludeId);

    /**
     * 检查学期名称是否存在
     *
     * @param semesterName 学期名称
     * @param excludeId 排除的学期ID
     * @return 是否存在
     */
    Boolean existsBySemesterName(@Param("semesterName") String semesterName, @Param("excludeId") Integer excludeId);

    /**
     * 获取当前学期
     *
     * @return 当前学期
     */
    Semester selectCurrentSemester();

    /**
     * 清除所有当前学期标记
     */
    void clearAllCurrentFlags();

    /**
     * 获取所有不重复的学年列表
     *
     * @return 学年列表
     */
    List<String> selectDistinctAcademicYears();

    /**
     * 设置当前学期
     *
     * @param semesterId 学期ID
     */
    void setCurrentSemester(@Param("semesterId") Integer semesterId);

    /**
     * 根据学年查询学期列表
     *
     * @param academicYear 学年
     * @return 学期列表
     */
    List<Semester> selectByAcademicYear(@Param("academicYear") String academicYear);
}
