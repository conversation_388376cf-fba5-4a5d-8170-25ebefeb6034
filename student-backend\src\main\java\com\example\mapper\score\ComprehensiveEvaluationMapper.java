package com.example.mapper.score;

import com.example.dto.score.ComprehensiveEvaluationQueryDTO;
import com.example.vo.score.ComprehensiveEvaluationVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 综合素质测评成绩对接Mapper接口
 *
 * <AUTHOR>
 * @since 2025-08-02
 */
@Mapper
public interface ComprehensiveEvaluationMapper {

    /**
     * 查询综合素质测评成绩列表
     *
     * @param queryDTO 查询条件
     * @return 综合素质测评成绩列表
     */
    List<ComprehensiveEvaluationVO> selectComprehensiveEvaluationList(@Param("query") ComprehensiveEvaluationQueryDTO queryDTO);

    /**
     * 查询综合素质测评成绩总数
     *
     * @param queryDTO 查询条件
     * @return 总数
     */
    Long selectComprehensiveEvaluationCount(@Param("query") ComprehensiveEvaluationQueryDTO queryDTO);
}
