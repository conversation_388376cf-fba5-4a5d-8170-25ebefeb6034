package com.example.mapper.score;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.example.dto.score.QualityEvaluationQueryDTO;
import com.example.entity.score.QualityEvaluation;
import com.example.vo.score.QualityEvaluationVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 基本素质测评Mapper接口
 *
 * <AUTHOR>
 * @since 2025-08-01
 */
@Mapper
public interface QualityEvaluationMapper extends BaseMapper<QualityEvaluation> {

    /**
     * 分页查询基本素质测评列表
     *
     * @param page 分页参数
     * @param queryDTO 查询条件
     * @return 分页结果
     */
    IPage<QualityEvaluationVO> selectQualityEvaluationPage(Page<QualityEvaluationVO> page, @Param("query") QualityEvaluationQueryDTO queryDTO);

    /**
     * 根据ID查询基本素质测评详情
     *
     * @param evaluationId 评估ID
     * @return 详情信息
     */
    QualityEvaluationVO selectQualityEvaluationById(@Param("evaluationId") Integer evaluationId);

    /**
     * 根据学号和学期查询基本素质测评
     *
     * @param studentId 学号
     * @param semesterId 学期ID
     * @return 基本素质测评信息
     */
    QualityEvaluation selectByStudentIdAndSemester(@Param("studentId") String studentId, @Param("semesterId") Integer semesterId);


}
