package com.example.service.score;

import com.example.dto.score.ComprehensiveEvaluationQueryDTO;
import com.example.vo.score.ComprehensiveEvaluationVO;

import java.util.List;
import java.util.Map;

/**
 * 综合素质测评成绩对接Service接口
 * 专注于导出功能：整合学业成绩（期末成绩）和基本素质测评成绩
 *
 * <AUTHOR>
 * @since 2025-08-02
 */
public interface ComprehensiveEvaluationService {

    /**
     * 根据学年查询综合素质测评成绩（用于预览）
     *
     * @param queryDTO 查询条件
     * @return 综合素质测评成绩列表
     */
    List<ComprehensiveEvaluationVO> getComprehensiveByAcademicYear(ComprehensiveEvaluationQueryDTO queryDTO);

    /**
     * 获取综合素质测评成绩列表
     *
     * @param queryDTO 查询条件
     * @return 综合素质测评成绩列表
     */
    List<ComprehensiveEvaluationVO> getComprehensiveEvaluationList(ComprehensiveEvaluationQueryDTO queryDTO);

    /**
     * 导出综合素质测评成绩Excel
     * 整合学业成绩（从期末成绩获取）和基本素质测评成绩，按70%:30%计算综合成绩
     *
     * @param queryDTO 查询条件
     * @return Excel文件字节数组
     */
    byte[] exportComprehensiveEvaluation(ComprehensiveEvaluationQueryDTO queryDTO);
}
