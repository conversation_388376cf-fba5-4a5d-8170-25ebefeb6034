package com.example.service.score;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.example.dto.score.QualityEvaluationQueryDTO;
import com.example.entity.score.QualityEvaluation;
import com.example.vo.score.QualityEvaluationVO;

import java.util.List;

/**
 * 基本素质测评Service接口
 *
 * <AUTHOR>
 * @since 2025-08-01
 */
public interface QualityEvaluationService extends IService<QualityEvaluation> {

    /**
     * 分页查询基本素质测评列表
     *
     * @param queryDTO 查询条件
     * @return 分页结果
     */
    IPage<QualityEvaluationVO> getQualityEvaluationPage(QualityEvaluationQueryDTO queryDTO);

    /**
     * 根据ID查询基本素质测评详情
     *
     * @param evaluationId 评估ID
     * @return 详情信息
     */
    QualityEvaluationVO getQualityEvaluationById(Integer evaluationId);

    /**
     * 保存基本素质测评
     *
     * @param qualityEvaluation 基本素质测评信息
     * @return 是否成功
     */
    boolean saveQualityEvaluation(QualityEvaluation qualityEvaluation);

    /**
     * 更新基本素质测评
     *
     * @param qualityEvaluation 基本素质测评信息
     * @return 是否成功
     */
    boolean updateQualityEvaluation(QualityEvaluation qualityEvaluation);

    /**
     * 删除基本素质测评
     *
     * @param evaluationId 评估ID
     * @return 是否成功
     */
    boolean deleteQualityEvaluation(Integer evaluationId);

    /**
     * 批量删除基本素质测评
     *
     * @param evaluationIds 评估ID列表
     * @return 是否成功
     */
    boolean batchDeleteQualityEvaluation(List<Integer> evaluationIds);

    /**
     * 根据学号和学期查询基本素质测评
     *
     * @param studentId 学号
     * @param semesterId 学期ID
     * @return 基本素质测评信息
     */
    QualityEvaluation getByStudentIdAndSemester(String studentId, Integer semesterId);

    /**
     * 计算总分
     *
     * @param qualityEvaluation 基本素质测评信息
     */
    void calculateTotalScore(QualityEvaluation qualityEvaluation);

    /**
     * 判断是否是第一学期
     * @param semesterId 学期ID
     * @return 是否是第一学期
     */
    boolean isFirstSemester(Integer semesterId);

    /**
     * 计算加分
     * @param addScoreRemark 加分说明
     * @return 加分值
     */
    double calculateAddScore(String addScoreRemark);

    /**
     * 计算扣分
     * @param reduceScoreRemark 扣分说明
     * @return 扣分值
     */
    double calculateReduceScore(String reduceScoreRemark);

}
