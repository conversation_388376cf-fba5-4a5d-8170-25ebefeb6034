package com.example.service.score.impl;

import com.example.dto.score.ComprehensiveEvaluationQueryDTO;
import com.example.mapper.score.ComprehensiveEvaluationMapper;
import com.example.service.score.ComprehensiveEvaluationService;
import com.example.common.excel.ApachePoiExportService;
import com.example.vo.score.ComprehensiveEvaluationVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * 综合素质测评成绩对接Service实现类
 * 专注于导出功能：整合学业成绩（期末成绩）和基本素质测评成绩
 *
 * <AUTHOR>
 * @since 2025-08-02
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class ComprehensiveEvaluationServiceImpl implements ComprehensiveEvaluationService {

    private final ComprehensiveEvaluationMapper comprehensiveEvaluationMapper;
    private final ApachePoiExportService apachePoiExportService;

    @Override
    public List<ComprehensiveEvaluationVO> getComprehensiveByAcademicYear(ComprehensiveEvaluationQueryDTO queryDTO) {
        try {
            log.info("查询综合素质测评成绩: 学年={}, 班级={}",
                    queryDTO.getAcademicYear(), queryDTO.getClassName());

            List<ComprehensiveEvaluationVO> result = comprehensiveEvaluationMapper.selectComprehensiveEvaluationList(queryDTO);

            log.info("查询到 {} 条综合素质测评成绩记录", result.size());
            return result;
        } catch (Exception e) {
            log.error("根据学年查询综合素质测评成绩失败", e);
            throw new RuntimeException("根据学年查询综合素质测评成绩失败: " + e.getMessage());
        }
    }

    @Override
    public List<ComprehensiveEvaluationVO> getComprehensiveEvaluationList(ComprehensiveEvaluationQueryDTO queryDTO) {
        try {
            log.info("获取综合素质测评成绩列表: 学年={}, 班级={}",
                    queryDTO.getAcademicYear(), queryDTO.getClassName());

            List<ComprehensiveEvaluationVO> result = comprehensiveEvaluationMapper.selectComprehensiveEvaluationList(queryDTO);

            log.info("获取到 {} 条综合素质测评成绩记录", result.size());
            return result;
        } catch (Exception e) {
            log.error("获取综合素质测评成绩列表失败", e);
            throw new RuntimeException("获取综合素质测评成绩列表失败: " + e.getMessage());
        }
    }

    @Override
    public byte[] exportComprehensiveEvaluation(ComprehensiveEvaluationQueryDTO queryDTO) {
        try {
            log.info("开始导出综合素质测评成绩Excel: 学年={}, 班级={}",
                    queryDTO.getAcademicYear(), queryDTO.getClassName());

            // 查询所有数据（不分页）
            queryDTO.setCurrentPage(null);
            queryDTO.setPageSize(null);
            queryDTO.setLimit(null);
            List<ComprehensiveEvaluationVO> dataList = comprehensiveEvaluationMapper.selectComprehensiveEvaluationList(queryDTO);

            if (dataList.isEmpty()) {
                log.warn("没有找到符合条件的综合素质测评成绩数据");
                throw new RuntimeException("没有数据可导出，请检查查询条件");
            }

            log.info("查询到 {} 条综合素质测评成绩记录，开始构建Excel数据", dataList.size());

            // 转换为Map格式，用于Excel导出
            List<Map<String, Object>> exportData = new ArrayList<>();
            for (int i = 0; i < dataList.size(); i++) {
                ComprehensiveEvaluationVO vo = dataList.get(i);
                Map<String, Object> map = new HashMap<>();
                map.put("rowNumber", i + 1);  // 序号，从1开始
                map.put("studentId", vo.getStudentId());
                map.put("studentName", vo.getStudentName());
                map.put("className", vo.getClassName());
                map.put("majorName", vo.getMajorName());
                map.put("academicYear", vo.getAcademicYear());
                // 学业成绩（从期末成绩获取）
                map.put("academicScore", vo.getAcademicScore() != null ? vo.getAcademicScore() : 0.0);
                // 基本素质测评成绩
                map.put("qualityScore", vo.getQualityScore() != null ? vo.getQualityScore() : 0.0);
                // 综合素质测评成绩（学业成绩*70% + 基本素质测评成绩*30%）
                map.put("comprehensiveScore", vo.getComprehensiveScore() != null ? vo.getComprehensiveScore() : 0.0);
                exportData.add(map);
            }

            // 构建表头映射
            Map<String, String> excelHeaders = new LinkedHashMap<>();
            excelHeaders.put("rowNumber", "序号");
            excelHeaders.put("studentId", "学号");
            excelHeaders.put("studentName", "姓名");
            excelHeaders.put("className", "班级");
            excelHeaders.put("majorName", "专业");
            excelHeaders.put("academicYear", "学年");
            excelHeaders.put("academicScore", "学业成绩(70%)");
            excelHeaders.put("qualityScore", "基本素质测评成绩(30%)");
            excelHeaders.put("comprehensiveScore", "综合素质测评成绩");

            // 使用ApachePoiExportService导出Excel
            byte[] excelData = apachePoiExportService.exportGradesToExcel(exportData, excelHeaders, "综合素质测评成绩");

            log.info("综合素质测评成绩Excel导出成功，文件大小: {} bytes", excelData.length);
            return excelData;

        } catch (Exception e) {
            log.error("导出综合素质测评成绩Excel失败", e);
            throw new RuntimeException("导出综合素质测评成绩Excel失败: " + e.getMessage());
        }
    }


}
