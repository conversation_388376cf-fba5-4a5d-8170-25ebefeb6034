package com.example.service.score.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.example.dto.score.QualityEvaluationQueryDTO;
import com.example.entity.score.QualityEvaluation;
import com.example.entity.student.Students;
import com.example.mapper.score.QualityEvaluationMapper;
import com.example.mapper.student.StudentsMapper;
import com.example.service.basic.SemesterService;
import com.example.service.score.QualityEvaluationService;
import com.example.vo.basic.SemesterVO;
import com.example.vo.score.QualityEvaluationVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 基本素质测评Service实现类
 *
 * <AUTHOR>
 * @since 2025-08-01
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class QualityEvaluationServiceImpl extends ServiceImpl<QualityEvaluationMapper, QualityEvaluation>
        implements QualityEvaluationService {

    private final QualityEvaluationMapper qualityEvaluationMapper;
    private final StudentsMapper studentsMapper;
    private final SemesterService semesterService;

    @Override
    public IPage<QualityEvaluationVO> getQualityEvaluationPage(QualityEvaluationQueryDTO queryDTO) {
        Page<QualityEvaluationVO> page = new Page<>(queryDTO.getCurrent(), queryDTO.getSize());
        return qualityEvaluationMapper.selectQualityEvaluationPage(page, queryDTO);
    }

    @Override
    public QualityEvaluationVO getQualityEvaluationById(Integer evaluationId) {
        return qualityEvaluationMapper.selectQualityEvaluationById(evaluationId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveQualityEvaluation(QualityEvaluation qualityEvaluation) {
        // 检查是否已存在相同学生和学期的记录
        QualityEvaluation existing = getByStudentIdAndSemester(
            qualityEvaluation.getStudentId(),
            qualityEvaluation.getSemesterId()
        );
        if (existing != null) {
            throw new RuntimeException("该学生在此学期的素质测评记录已存在");
        }

        // 计算总分（包含加分扣分计算）
        calculateTotalScore(qualityEvaluation);

        // 设置创建时间
        qualityEvaluation.setCreateTime(LocalDateTime.now());
        qualityEvaluation.setUpdateTime(LocalDateTime.now());

        return save(qualityEvaluation);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateQualityEvaluation(QualityEvaluation qualityEvaluation) {
        // 计算总分（包含加分扣分计算）
        calculateTotalScore(qualityEvaluation);

        // 设置更新时间
        qualityEvaluation.setUpdateTime(LocalDateTime.now());

        return updateById(qualityEvaluation);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteQualityEvaluation(Integer evaluationId) {
        return removeById(evaluationId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchDeleteQualityEvaluation(List<Integer> evaluationIds) {
        if (evaluationIds == null || evaluationIds.isEmpty()) {
            return false;
        }

        return removeByIds(evaluationIds);
    }

    @Override
    public QualityEvaluation getByStudentIdAndSemester(String studentId, Integer semesterId) {
        return qualityEvaluationMapper.selectByStudentIdAndSemester(studentId, semesterId);
    }

    @Override
    public void calculateTotalScore(QualityEvaluation qualityEvaluation) {
        if (qualityEvaluation == null) {
            return;
        }

        // 计算加分和扣分
        if (qualityEvaluation.getAddScoreRemark() != null) {
            qualityEvaluation.setAddScore(BigDecimal.valueOf(calculateAddScore(qualityEvaluation.getAddScoreRemark())));
        }

        if (qualityEvaluation.getReduceScoreRemark() != null) {
            qualityEvaluation.setReduceScore(BigDecimal.valueOf(calculateReduceScore(qualityEvaluation.getReduceScoreRemark())));
        }

        // 获取基础分
        double baseScore = 0;

        // 判断是否是第一学期
        boolean isFirstSemester = isFirstSemester(qualityEvaluation.getSemesterId());

        if (isFirstSemester) {
            // 第一学期基础分为60
            baseScore = 60;
            qualityEvaluation.setPeriodScore(BigDecimal.valueOf(baseScore));
            qualityEvaluation.setPrevPeriodScore(null); // 第一学期没有上一学期记录
        } else {
            // 第二学期基础分为第一学期的总分
            baseScore = getPreviousSemesterTotalScore(qualityEvaluation.getStudentId(), qualityEvaluation.getSemesterId());
            qualityEvaluation.setPeriodScore(BigDecimal.valueOf(baseScore));
            qualityEvaluation.setPrevPeriodScore(BigDecimal.valueOf(baseScore));
        }

        // 计算总分 = 基础分 + 加分 - 扣分
        double addScore = qualityEvaluation.getAddScore() != null ? qualityEvaluation.getAddScore().doubleValue() : 0;
        double reduceScore = qualityEvaluation.getReduceScore() != null ? qualityEvaluation.getReduceScore().doubleValue() : 0;
        double totalScore = baseScore + addScore - reduceScore;

        // 确保总分不为负数
        if (totalScore < 0) {
            totalScore = 0;
        }

        // 保留两位小数
        totalScore = new BigDecimal(totalScore).setScale(2, RoundingMode.HALF_UP).doubleValue();

        // 设置总分
        qualityEvaluation.setTotalScore(BigDecimal.valueOf(totalScore));
    }





    @Override
    public boolean isFirstSemester(Integer semesterId) {
        if (semesterId == null) {
            return true; // 默认为第一学期
        }

        try {
            // 根据学期ID查询学期信息
            SemesterVO semester = semesterService.getSemesterById(semesterId);
            if (semester != null && semester.getSemesterNumber() != null) {
                // 学期号为1表示第一学期，为2表示第二学期
                return semester.getSemesterNumber() == 1;
            }
        } catch (Exception e) {
            log.warn("查询学期信息失败，学期ID: {}", semesterId, e);
        }

        // 如果查询失败，使用学期ID的奇偶性判断
        // 奇数ID为第一学期，偶数ID为第二学期
        return semesterId % 2 == 1;
    }

    /**
     * 获取上一学期的总分
     * @param studentId 学号
     * @param currentSemesterId 当前学期ID
     * @return 上一学期总分，如果没有找到则返回60
     */
    private double getPreviousSemesterTotalScore(String studentId, Integer currentSemesterId) {
        if (studentId == null || currentSemesterId == null) {
            return 60; // 默认基础分
        }

        try {
            // 获取当前学期信息
            SemesterVO currentSemester = semesterService.getSemesterById(currentSemesterId);
            if (currentSemester == null) {
                return 60;
            }

            // 查找同一学年的第一学期
            String academicYear = currentSemester.getAcademicYear();
            List<SemesterVO> semesters = semesterService.getSemestersByAcademicYear(academicYear);

            Integer firstSemesterId = null;
            for (SemesterVO semester : semesters) {
                if (semester.getSemesterNumber() != null && semester.getSemesterNumber() == 1) {
                    firstSemesterId = semester.getId();
                    break;
                }
            }

            if (firstSemesterId == null) {
                log.warn("未找到学年 {} 的第一学期", academicYear);
                return 60;
            }

            // 查询该学生第一学期的素质测评记录
            QualityEvaluation firstSemesterRecord = getByStudentIdAndSemester(studentId, firstSemesterId);
            if (firstSemesterRecord != null && firstSemesterRecord.getTotalScore() != null) {
                return firstSemesterRecord.getTotalScore().doubleValue();
            }

            log.info("学生 {} 在第一学期（学期ID: {}）没有素质测评记录，使用默认基础分60", studentId, firstSemesterId);
            return 60;

        } catch (Exception e) {
            log.error("查询上一学期总分失败，学生: {}, 当前学期ID: {}", studentId, currentSemesterId, e);
            return 60; // 出错时返回默认基础分
        }
    }

    @Override
    public double calculateAddScore(String addScoreRemark) {
        if (addScoreRemark == null || addScoreRemark.isEmpty()) {
            return 0;
        }

        List<ScoreItem> items = parseRemarkItems(addScoreRemark);
        double total = 0;

        for (ScoreItem item : items) {
            if ("+".equals(item.getSign())) {
                total += item.getScore();
            }
        }

        // 保留两位小数
        return new BigDecimal(total).setScale(2, RoundingMode.HALF_UP).doubleValue();
    }

    @Override
    public double calculateReduceScore(String reduceScoreRemark) {
        if (reduceScoreRemark == null || reduceScoreRemark.isEmpty()) {
            return 0;
        }

        List<ScoreItem> items = parseRemarkItems(reduceScoreRemark);
        double total = 0;

        for (ScoreItem item : items) {
            if ("-".equals(item.getSign())) {
                total += item.getScore();
            }
        }

        // 保留两位小数
        return new BigDecimal(total).setScale(2, RoundingMode.HALF_UP).doubleValue();
    }

    /**
     * 解析备注中的分数项
     */
    private List<ScoreItem> parseRemarkItems(String remark) {
        List<ScoreItem> items = new ArrayList<>();
        if (remark == null || remark.isEmpty()) {
            return items;
        }

        // 按行分割
        String[] lines = remark.split("\n");

        for (String line : lines) {
            // 去除空行
            line = line.trim();
            if (line.isEmpty()) {
                continue;
            }

            // 移除可能的序号前缀，如 "1. " 或 "1、"
            Pattern prefixPattern = Pattern.compile("^(\\d+[\\.、])\\s*");
            Matcher prefixMatcher = prefixPattern.matcher(line);
            if (prefixMatcher.find()) {
                line = line.substring(prefixMatcher.end());
            }

            // 尝试多种模式匹配加减分
            boolean matched = false;

            // 模式0：带有乘法表达式的模式，如 "-2*2" 或 "+3*5"
            Pattern multiplyPattern = Pattern.compile("(.+?)\\s*(\\+|\\-)(\\d+(\\.\\d+)?)\\*(\\d+)\\s*$");
            Matcher multiplyMatcher = multiplyPattern.matcher(line);

            if (multiplyMatcher.find()) {
                String text = multiplyMatcher.group(1).trim();
                String sign = multiplyMatcher.group(2);
                double baseScore = Double.parseDouble(multiplyMatcher.group(3));
                int multiplier = Integer.parseInt(multiplyMatcher.group(5));
                double score = baseScore * multiplier;

                items.add(new ScoreItem(text, score, sign));
                matched = true;
            }

            // 模式1：末尾有明确的加减分数值 "+5", "-2"等
            if (!matched) {
                Pattern endScorePattern = Pattern.compile("(.+?)\\s*(\\+|\\-)(\\d+(\\.\\d+)?)\\s*$");
                Matcher endScoreMatcher = endScorePattern.matcher(line);

                if (endScoreMatcher.find()) {
                    String text = endScoreMatcher.group(1).trim();
                    String sign = endScoreMatcher.group(2);
                    double score = Double.parseDouble(endScoreMatcher.group(3));

                    items.add(new ScoreItem(text, score, sign));
                    matched = true;
                }
            }

            // 模式2：字符串中包含带乘法的 "+数字*数字" 或 "-数字*数字" 格式
            if (!matched) {
                Pattern inlineMultiplyPattern = Pattern.compile("(.+)(\\+|\\-)(\\d+(\\.\\d+)?)\\*(\\d+)(.*)");
                Matcher inlineMultiplyMatcher = inlineMultiplyPattern.matcher(line);

                if (inlineMultiplyMatcher.find()) {
                    String prefix = inlineMultiplyMatcher.group(1).trim();
                    String sign = inlineMultiplyMatcher.group(2);
                    double baseScore = Double.parseDouble(inlineMultiplyMatcher.group(3));
                    int multiplier = Integer.parseInt(inlineMultiplyMatcher.group(5));
                    String suffix = inlineMultiplyMatcher.group(6).trim();

                    double score = baseScore * multiplier;

                    String text = prefix;
                    if (!suffix.isEmpty()) {
                        text += " " + suffix;
                    }

                    items.add(new ScoreItem(text, score, sign));
                    matched = true;
                }
            }

            // 模式3：字符串中包含 "+数字" 或 "-数字" 格式
            if (!matched) {
                Pattern inlineScorePattern = Pattern.compile("(.+)(\\+|\\-)(\\d+(\\.\\d+)?)(.*)");
                Matcher inlineScoreMatcher = inlineScorePattern.matcher(line);

                if (inlineScoreMatcher.find()) {
                    String prefix = inlineScoreMatcher.group(1).trim();
                    String sign = inlineScoreMatcher.group(2);
                    double score = Double.parseDouble(inlineScoreMatcher.group(3));
                    String suffix = inlineScoreMatcher.group(5).trim();

                    String text = prefix;
                    if (!suffix.isEmpty()) {
                        text += " " + suffix;
                    }

                    items.add(new ScoreItem(text, score, sign));
                    matched = true;
                }
            }

            // 如果都没有匹配到，记录为无分数项
            if (!matched) {
                log.debug("无法解析分数项: {}", line);
            }
        }

        return items;
    }

    /**
     * 加减分项内部类
     */
    private static class ScoreItem {
        private String text;
        private double score;
        private String sign;

        public ScoreItem(String text, double score, String sign) {
            this.text = text;
            this.score = score;
            this.sign = sign;
        }

        public String getText() {
            return text;
        }

        public double getScore() {
            return score;
        }

        public String getSign() {
            return sign;
        }
    }

}
