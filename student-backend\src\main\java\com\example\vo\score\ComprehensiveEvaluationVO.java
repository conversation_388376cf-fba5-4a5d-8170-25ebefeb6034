package com.example.vo.score;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 综合素质测评成绩VO
 *
 * <AUTHOR>
 * @since 2025-08-02
 */
@Data
@Schema(description = "综合素质测评成绩VO")
public class ComprehensiveEvaluationVO {

    @Schema(description = "学生ID")
    private String studentId;

    @Schema(description = "学生姓名")
    private String studentName;

    @Schema(description = "班级代码")
    private String classCode;

    @Schema(description = "班级名称")
    private String className;

    @Schema(description = "专业代码")
    private String majorCode;

    @Schema(description = "专业名称")
    private String majorName;

    @Schema(description = "学院代码")
    private String collegeCode;

    @Schema(description = "学院名称")
    private String collegeName;

    @Schema(description = "学年")
    private String academicYear;



    @Schema(description = "学业成绩")
    private Double academicScore;

    @Schema(description = "基本素质测评成绩")
    private Double qualityScore;

    @Schema(description = "综合素质测评成绩")
    private Double comprehensiveScore;

    @Schema(description = "平均分")
    private Double averageScore;



    @Schema(description = "总学分")
    private Double totalCredits;

    @Schema(description = "排名")
    private Integer ranking;

    @Schema(description = "创建时间")
    private String createTime;

    @Schema(description = "更新时间")
    private String updateTime;
}
