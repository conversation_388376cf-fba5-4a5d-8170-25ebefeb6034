package com.example.vo.score;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 基本素质测评返回VO
 *
 * <AUTHOR>
 * @since 2025-08-01
 */
@Data
public class QualityEvaluationVO {

    /**
     * 主键ID
     */
    private Integer evaluationId;

    /**
     * 学号
     */
    private String studentId;

    /**
     * 学生姓名
     */
    private String studentName;

    /**
     * 宿舍号
     */
    private String dormitory;

    /**
     * 班级代码
     */
    private String classCode;

    /**
     * 班级名称
     */
    private String className;

    /**
     * 专业代码
     */
    private String majorCode;

    /**
     * 专业名称
     */
    private String majorName;

    /**
     * 学院代码
     */
    private String collegeCode;

    /**
     * 学院名称
     */
    private String collegeName;

    /**
     * 加分项
     */
    private BigDecimal addScore;

    /**
     * 扣分项
     */
    private BigDecimal reduceScore;

    /**
     * 加分说明
     */
    private String addScoreRemark;

    /**
     * 扣分说明
     */
    private String reduceScoreRemark;

    /**
     * 学期ID
     */
    private Integer semesterId;

    /**
     * 学年
     */
    private String academicYear;

    /**
     * 学期号
     */
    private Integer semesterNumber;

    /**
     * 学期名称
     */
    private String semesterName;

    /**
     * 周期得分
     */
    private BigDecimal periodScore;

    /**
     * 总得分
     */
    private BigDecimal totalScore;

    /**
     * 上一学期总分
     */
    private BigDecimal prevPeriodScore;



    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}
