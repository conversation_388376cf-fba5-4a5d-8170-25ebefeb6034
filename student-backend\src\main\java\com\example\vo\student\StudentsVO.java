package com.example.vo.student;

import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 学生信息VO类
 */
@Data
public class StudentsVO {

    /**
     * 学生ID
     */
    private Integer id;

    /**
     * 学号
     */
    private String studentId;

    /**
     * 姓名
     */
    private String name;

    /**
     * 性别
     */
    private String gender;

    /**
     * 出生日期
     */
    private LocalDate birthDate;

    /**
     * 身份证号
     */
    private String idCard;

    /**
     * 电话号码
     */
    private String phone;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 所属班级代码
     */
    private String classCode;

    /**
     * 宿舍号
     */
    private String dormitory;

    /**
     * 班级名称（关联查询）
     */
    private String className;

    /**
     * 专业代码（关联查询）
     */
    private String majorCode;

    /**
     * 专业名称（关联查询）
     */
    private String majorName;

    /**
     * 学院代码（关联查询）
     */
    private String collegeCode;

    /**
     * 学院名称（关联查询）
     */
    private String collegeName;

    /**
     * 入学日期
     */
    private LocalDate enrollmentDate;

    /**
     * 学籍状态
     */
    private String status;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;
}
