<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.mapper.basic.SemesterMapper">

    <!-- 基础结果映射 -->
    <resultMap id="BaseResultMap" type="com.example.entity.basic.Semester">
        <id column="id" property="id" jdbcType="INTEGER"/>
        <result column="academic_year" property="academicYear" jdbcType="VARCHAR"/>
        <result column="semester_number" property="semesterNumber" jdbcType="TINYINT"/>
        <result column="semester_name" property="semesterName" jdbcType="VARCHAR"/>
        <result column="start_date" property="startDate" jdbcType="DATE"/>
        <result column="end_date" property="endDate" jdbcType="DATE"/>
        <result column="is_current" property="isCurrent" jdbcType="BOOLEAN"/>
        <result column="created_at" property="createdAt" jdbcType="TIMESTAMP"/>
        <result column="updated_at" property="updatedAt" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 基础字段 -->
    <sql id="Base_Column_List">
        id, academic_year, semester_number, semester_name, start_date, end_date, is_current, created_at, updated_at
    </sql>

    <!-- 分页查询学年学期列表 -->
    <select id="selectSemesterPage" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM semesters
        <where>
            <if test="query.academicYear != null and query.academicYear != ''">
                AND academic_year LIKE CONCAT('%', #{query.academicYear}, '%')
            </if>
            <if test="query.semesterNumber != null">
                AND semester_number = #{query.semesterNumber}
            </if>
            <if test="query.semesterName != null and query.semesterName != ''">
                AND semester_name LIKE CONCAT('%', #{query.semesterName}, '%')
            </if>
            <if test="query.isCurrent != null">
                AND is_current = #{query.isCurrent}
            </if>
        </where>
        ORDER BY academic_year DESC, semester_number DESC
    </select>

    <!-- 查询所有学年学期 -->
    <select id="selectAllSemesters" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM semesters
        ORDER BY academic_year DESC, semester_number DESC
    </select>

    <!-- 根据学年和学期号查询学期 -->
    <select id="selectByAcademicYearAndNumber" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM semesters
        WHERE academic_year = #{academicYear} AND semester_number = #{semesterNumber}
    </select>

    <!-- 检查学年学期是否存在 -->
    <select id="existsByAcademicYearAndNumber" resultType="java.lang.Boolean">
        SELECT COUNT(1) > 0
        FROM semesters
        WHERE academic_year = #{academicYear} AND semester_number = #{semesterNumber}
        <if test="excludeId != null">
            AND id != #{excludeId}
        </if>
    </select>

    <!-- 检查学期名称是否存在 -->
    <select id="existsBySemesterName" resultType="java.lang.Boolean">
        SELECT COUNT(1) > 0
        FROM semesters
        WHERE semester_name = #{semesterName}
        <if test="excludeId != null">
            AND id != #{excludeId}
        </if>
    </select>

    <!-- 获取当前学期 -->
    <select id="selectCurrentSemester" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM semesters
        WHERE is_current = 1
        LIMIT 1
    </select>

    <!-- 清除所有当前学期标记 -->
    <update id="clearAllCurrentFlags">
        UPDATE semesters SET is_current = 0
    </update>

    <!-- 设置当前学期 -->
    <update id="setCurrentSemester">
        UPDATE semesters SET is_current = 1 WHERE id = #{semesterId}
    </update>

    <!-- 根据学年查询学期列表 -->
    <select id="selectByAcademicYear" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM semesters
        WHERE academic_year = #{academicYear}
        ORDER BY semester_number ASC
    </select>

    <!-- 获取所有不重复的学年列表 -->
    <select id="selectDistinctAcademicYears" resultType="java.lang.String">
        SELECT DISTINCT academic_year
        FROM semesters
        ORDER BY academic_year DESC
    </select>

</mapper>
