<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.mapper.score.ComprehensiveEvaluationMapper">

    <!-- 综合素质测评成绩VO结果映射 -->
    <resultMap id="ComprehensiveEvaluationVOMap" type="com.example.vo.score.ComprehensiveEvaluationVO">
        <result column="student_id" property="studentId"/>
        <result column="student_name" property="studentName"/>
        <result column="class_code" property="classCode"/>
        <result column="class_name" property="className"/>
        <result column="major_code" property="majorCode"/>
        <result column="major_name" property="majorName"/>
        <result column="college_code" property="collegeCode"/>
        <result column="college_name" property="collegeName"/>
        <result column="academic_year" property="academicYear"/>
        <result column="semester_id" property="semesterId"/>
        <result column="semester_name" property="semesterName"/>
        <result column="academic_score" property="academicScore"/>
        <result column="quality_score" property="qualityScore"/>
        <result column="comprehensive_score" property="comprehensiveScore"/>
        <result column="average_score" property="averageScore"/>
        <result column="gpa" property="gpa"/>
        <result column="total_credits" property="totalCredits"/>
        <result column="ranking" property="ranking"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <!-- 基础查询SQL -->
    <sql id="selectComprehensiveEvaluationVo">
        SELECT
            s.student_id,
            s.name as student_name,
            s.class_code,
            c.class_name,
            c.major_code,
            m.major_name,
            m.college_code,
            col.college_name,
            sem.academic_year,
            -- 学业成绩：计算学生在该学年的平均成绩
            COALESCE(ROUND(AVG(g.final_score), 2), 0) as academic_score,
            -- 基本素质测评成绩：获取该学年第二学期的总分
            COALESCE(
                (SELECT qe.total_score
                 FROM quality_evaluation qe
                 JOIN semesters s2 ON qe.semester_id = s2.id
                 WHERE qe.student_id = s.student_id
                   AND s2.academic_year = sem.academic_year
                   AND s2.semester_number = 2
                 ORDER BY qe.create_time DESC
                 LIMIT 1), 0
            ) as quality_score,
            -- 综合素质测评成绩：学业成绩 * 0.7 + 基本素质测评成绩(第二学期) * 0.3
            COALESCE(
                ROUND(
                    AVG(g.final_score) * 0.7 +
                    COALESCE(
                        (SELECT qe.total_score
                         FROM quality_evaluation qe
                         JOIN semesters s2 ON qe.semester_id = s2.id
                         WHERE qe.student_id = s.student_id
                           AND s2.academic_year = sem.academic_year
                           AND s2.semester_number = 2
                         ORDER BY qe.create_time DESC
                         LIMIT 1), 0
                    ) * 0.3, 2
                ), 0
            ) as comprehensive_score,
            -- 平均分
            COALESCE(ROUND(AVG(g.final_score), 2), 0) as average_score,
            -- 总学分
            COALESCE(SUM(DISTINCT co.credits), 0) as total_credits,
            -- 排名（暂时设为NULL，可后续计算）
            NULL as ranking,
            NOW() as create_time,
            NOW() as update_time
        FROM students s
        LEFT JOIN classes c ON s.class_code = c.class_code
        LEFT JOIN majors m ON c.major_code = m.major_code
        LEFT JOIN colleges col ON m.college_code = col.college_code
        LEFT JOIN grades g ON s.student_id = g.student_id
        LEFT JOIN courses co ON g.course_code = co.course_code
        LEFT JOIN semesters sem ON g.semester_id = sem.id
    </sql>

    <!-- 查询条件 -->
    <sql id="selectComprehensiveEvaluationWhere">
        <where>
            <if test="query.academicYear != null and query.academicYear != ''">
                AND sem.academic_year = #{query.academicYear}
            </if>

            <if test="query.studentId != null and query.studentId != ''">
                AND s.student_id = #{query.studentId}
            </if>
            <if test="query.studentName != null and query.studentName != ''">
                AND s.name LIKE CONCAT('%', #{query.studentName}, '%')
            </if>
            <if test="query.className != null and query.className != ''">
                AND c.class_name LIKE CONCAT('%', #{query.className}, '%')
            </if>
            <if test="query.majorCode != null and query.majorCode != ''">
                AND c.major_code = #{query.majorCode}
            </if>
            <if test="query.collegeCode != null and query.collegeCode != ''">
                AND m.college_code = #{query.collegeCode}
            </if>
        </where>
    </sql>

    <!-- 查询综合素质测评成绩列表 -->
    <select id="selectComprehensiveEvaluationList" parameterType="com.example.dto.score.ComprehensiveEvaluationQueryDTO"
            resultMap="ComprehensiveEvaluationVOMap">
        <include refid="selectComprehensiveEvaluationVo"/>
        <include refid="selectComprehensiveEvaluationWhere"/>
        GROUP BY s.student_id, sem.academic_year
        ORDER BY s.student_id, sem.academic_year DESC
        <if test="query.limit != null">
            LIMIT #{query.limit}
        </if>
    </select>

    <!-- 查询综合素质测评成绩总数 -->
    <select id="selectComprehensiveEvaluationCount" parameterType="com.example.dto.score.ComprehensiveEvaluationQueryDTO"
            resultType="java.lang.Long">
        SELECT COUNT(DISTINCT CONCAT(s.student_id, '_', sem.academic_year))
        FROM students s
        LEFT JOIN classes c ON s.class_code = c.class_code
        LEFT JOIN majors m ON c.major_code = m.major_code
        LEFT JOIN colleges col ON m.college_code = col.college_code
        LEFT JOIN grades g ON s.student_id = g.student_id
        LEFT JOIN courses co ON g.course_code = co.course_code
        LEFT JOIN semesters sem ON g.semester_id = sem.id
        <include refid="selectComprehensiveEvaluationWhere"/>
    </select>

</mapper>
