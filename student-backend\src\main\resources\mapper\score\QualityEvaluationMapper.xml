<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.mapper.score.QualityEvaluationMapper">

    <!-- 基本素质测评VO结果映射 -->
    <resultMap id="QualityEvaluationVOMap" type="com.example.vo.score.QualityEvaluationVO">
        <id column="evaluation_id" property="evaluationId"/>
        <result column="student_id" property="studentId"/>
        <result column="student_name" property="studentName"/>
        <result column="dormitory" property="dormitory"/>
        <result column="class_code" property="classCode"/>
        <result column="class_name" property="className"/>
        <result column="major_code" property="majorCode"/>
        <result column="major_name" property="majorName"/>
        <result column="college_code" property="collegeCode"/>
        <result column="college_name" property="collegeName"/>
        <result column="add_score" property="addScore"/>
        <result column="reduce_score" property="reduceScore"/>
        <result column="add_score_remark" property="addScoreRemark"/>
        <result column="reduce_score_remark" property="reduceScoreRemark"/>
        <result column="semester_id" property="semesterId"/>
        <result column="academic_year" property="academicYear"/>
        <result column="semester_number" property="semesterNumber"/>
        <result column="semester_name" property="semesterName"/>
        <result column="period_score" property="periodScore"/>
        <result column="total_score" property="totalScore"/>
        <result column="prev_period_score" property="prevPeriodScore"/>

        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <!-- 基础查询字段 -->
    <sql id="selectQualityEvaluationVo">
        SELECT
            qe.evaluation_id,
            qe.student_id,
            s.name as student_name,
            s.dormitory,
            c.class_code,
            c.class_name,
            m.major_code,
            m.major_name,
            col.college_code,
            col.college_name,
            qe.add_score,
            qe.reduce_score,
            qe.add_score_remark,
            qe.reduce_score_remark,
            qe.semester_id,
            sem.academic_year,
            sem.semester_number,
            CONCAT(sem.academic_year, '-', sem.semester_number) as semester_name,
            qe.period_score,
            qe.total_score,
            qe.prev_period_score,

            qe.create_time,
            qe.update_time
        FROM quality_evaluation qe
        LEFT JOIN students s ON qe.student_id = s.student_id
        LEFT JOIN classes c ON s.class_code = c.class_code
        LEFT JOIN majors m ON c.major_code = m.major_code
        LEFT JOIN colleges col ON m.college_code = col.college_code
        LEFT JOIN semesters sem ON qe.semester_id = sem.id
    </sql>

    <!-- 分页查询基本素质测评列表 -->
    <select id="selectQualityEvaluationPage" resultMap="QualityEvaluationVOMap">
        <include refid="selectQualityEvaluationVo"/>
        <where>
            <if test="query.studentId != null and query.studentId != ''">
                AND qe.student_id = #{query.studentId}
            </if>
            <if test="query.studentName != null and query.studentName != ''">
                AND s.name LIKE CONCAT('%', #{query.studentName}, '%')
            </if>
            <if test="query.dormitory != null and query.dormitory != ''">
                AND s.dormitory = #{query.dormitory}
            </if>
            <if test="query.classCode != null and query.classCode != ''">
                AND c.class_code = #{query.classCode}
            </if>
            <if test="query.majorCode != null and query.majorCode != ''">
                AND m.major_code = #{query.majorCode}
            </if>
            <if test="query.collegeCode != null and query.collegeCode != ''">
                AND col.college_code = #{query.collegeCode}
            </if>
            <if test="query.semesterId != null">
                AND qe.semester_id = #{query.semesterId}
            </if>
            <if test="query.academicYear != null and query.academicYear != ''">
                AND sem.academic_year = #{query.academicYear}
            </if>
            <if test="query.semesterNumber != null">
                AND sem.semester_number = #{query.semesterNumber}
            </if>
            <if test="query.minTotalScore != null">
                AND qe.total_score >= #{query.minTotalScore}
            </if>
            <if test="query.maxTotalScore != null">
                AND qe.total_score &lt;= #{query.maxTotalScore}
            </if>
        </where>
        ORDER BY sem.academic_year DESC, sem.semester_number DESC, s.student_id ASC
    </select>

    <!-- 根据ID查询基本素质测评详情 -->
    <select id="selectQualityEvaluationById" resultMap="QualityEvaluationVOMap">
        <include refid="selectQualityEvaluationVo"/>
        WHERE qe.evaluation_id = #{evaluationId}
    </select>

    <!-- 根据学号和学期查询基本素质测评 -->
    <select id="selectByStudentIdAndSemester" resultType="com.example.entity.score.QualityEvaluation">
        SELECT * FROM quality_evaluation
        WHERE student_id = #{studentId} AND semester_id = #{semesterId}
    </select>

</mapper>
