<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.mapper.student.StudentsMapper">

    <!-- 结果映射 -->
    <resultMap id="StudentsVOResultMap" type="com.example.vo.student.StudentsVO">
        <id column="id" property="id"/>
        <result column="student_id" property="studentId"/>
        <result column="name" property="name"/>
        <result column="gender" property="gender"/>
        <result column="birth_date" property="birthDate"/>
        <result column="id_card" property="idCard"/>
        <result column="phone" property="phone"/>
        <result column="email" property="email"/>
        <result column="class_code" property="classCode"/>
        <result column="dormitory" property="dormitory"/>
        <result column="class_name" property="className"/>
        <result column="major_code" property="majorCode"/>
        <result column="major_name" property="majorName"/>
        <result column="college_code" property="collegeCode"/>
        <result column="college_name" property="collegeName"/>
        <result column="enrollment_date" property="enrollmentDate"/>
        <result column="status" property="status"/>
        <result column="created_at" property="createdAt"/>
        <result column="updated_at" property="updatedAt"/>
    </resultMap>

    <!-- 通用SQL片段 -->
    <sql id="Base_Column_List">
        s.id, s.student_id, s.name, s.gender, s.birth_date, s.id_card,
        s.phone, s.email, s.class_code, s.dormitory, s.enrollment_date, s.status,
        s.created_at, s.updated_at,
        c.class_name, c.major_code, m.major_name, m.college_code, col.college_name
    </sql>

    <!-- 通用表连接 -->
    <sql id="Base_Join">
        FROM students s
        LEFT JOIN classes c ON s.class_code = c.class_code
        LEFT JOIN majors m ON c.major_code = m.major_code
        LEFT JOIN colleges col ON m.college_code = col.college_code
    </sql>

    <!-- 查询条件 -->
    <sql id="Query_Condition">
        <where>
            <if test="query.studentId != null and query.studentId != ''">
                AND s.student_id LIKE CONCAT('%', #{query.studentId}, '%')
            </if>
            <if test="query.name != null and query.name != ''">
                AND s.name LIKE CONCAT('%', #{query.name}, '%')
            </if>
            <if test="query.gender != null and query.gender != ''">
                AND s.gender = #{query.gender}
            </if>
            <if test="query.classCode != null and query.classCode != ''">
                AND s.class_code = #{query.classCode}
            </if>
            <if test="query.majorCode != null and query.majorCode != ''">
                AND c.major_code = #{query.majorCode}
            </if>
            <if test="query.collegeCode != null and query.collegeCode != ''">
                AND m.college_code = #{query.collegeCode}
            </if>
            <if test="query.status != null and query.status != ''">
                AND s.status = #{query.status}
            </if>
            <if test="query.enrollmentYear != null">
                AND YEAR(s.enrollment_date) = #{query.enrollmentYear}
            </if>
        </where>
    </sql>

    <!-- 分页查询学生列表 -->
    <select id="selectStudentsPage" resultMap="StudentsVOResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        <include refid="Base_Join"/>
        <include refid="Query_Condition"/>
        ORDER BY
        <choose>
            <when test="query.sortField == 'collegeName'">CONVERT(col.college_name USING gbk)</when>
            <when test="query.sortField == 'majorName'">CONVERT(m.major_name USING gbk)</when>
            <when test="query.sortField == 'className'">CONVERT(c.class_name USING gbk)</when>
            <when test="query.sortField == 'studentId'">s.student_id</when>
            <when test="query.sortField == 'name'">CONVERT(s.name USING gbk)</when>
            <when test="query.sortField == 'enrollmentDate'">s.enrollment_date</when>
            <when test="query.sortField == 'createdAt'">s.created_at</when>
            <otherwise>CONVERT(col.college_name USING gbk)</otherwise>
        </choose>
        <if test="query.sortOrder == 'desc'">DESC</if>
        <if test="query.sortOrder != 'desc'">ASC</if>
        , CONVERT(m.major_name USING gbk) ASC, CONVERT(c.class_name USING gbk) ASC, s.student_id ASC
    </select>

    <!-- 根据班级代码查询学生列表 -->
    <select id="selectStudentsByClassCode" resultMap="StudentsVOResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        <include refid="Base_Join"/>
        WHERE s.class_code = #{classCode}
        ORDER BY CONVERT(col.college_name USING gbk) ASC, CONVERT(m.major_name USING gbk) ASC, CONVERT(c.class_name USING gbk) ASC, s.student_id ASC
    </select>

    <!-- 根据专业代码查询学生列表 -->
    <select id="selectStudentsByMajorCode" resultMap="StudentsVOResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        <include refid="Base_Join"/>
        WHERE c.major_code = #{majorCode}
        ORDER BY CONVERT(col.college_name USING gbk) ASC, CONVERT(m.major_name USING gbk) ASC, CONVERT(c.class_name USING gbk) ASC, s.student_id ASC
    </select>

    <!-- 检查学号是否存在 -->
    <select id="existsStudentId" resultType="boolean">
        SELECT COUNT(1) > 0
        FROM students
        WHERE student_id = #{studentId}
        <if test="excludeId != null">
            AND id != #{excludeId}
        </if>
    </select>

    <!-- 根据班级代码统计学生人数 -->
    <select id="countStudentsByClassCode" resultType="int">
        SELECT COUNT(1)
        FROM students
        WHERE class_code = #{classCode} AND status = '在校'
    </select>

    <!-- 根据班级代码获取班级ID -->
    <select id="getClassIdByClassCode" resultType="java.lang.Integer">
        SELECT id
        FROM classes
        WHERE class_code = #{classCode}
    </select>

</mapper>
