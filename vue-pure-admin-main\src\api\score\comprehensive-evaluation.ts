import { http } from "@/utils/http";

export interface ComprehensiveEvaluationVO {
  studentId: string;
  studentName?: string;
  classCode?: string;
  className?: string;
  majorCode?: string;
  majorName?: string;
  collegeCode?: string;
  collegeName?: string;
  academicYear?: string;
  academicScore?: number;
  qualityScore?: number;
  comprehensiveScore?: number;
  averageScore?: number;
  totalCredits?: number;
  ranking?: number;
  createTime?: string;
  updateTime?: string;
}

export interface ComprehensiveEvaluationQueryDTO {
  academicYear?: string;
  studentId?: string;
  studentName?: string;
  className?: string;
  majorCode?: string;
  collegeCode?: string;
  minAcademicScore?: number;
  maxAcademicScore?: number;
  minQualityScore?: number;
  maxQualityScore?: number;
  minComprehensiveScore?: number;
  maxComprehensiveScore?: number;
  currentPage?: number;
  pageSize?: number;
  limit?: number;
}

export interface ComprehensiveEvaluationPageVO {
  records: ComprehensiveEvaluationVO[];
  total: number;
  current: number;
  size: number;
}

export interface HorizontalComprehensiveData {
  students: ComprehensiveEvaluationVO[];
  total: number;
  currentPage: number;
  pageSize: number;
}

/** 分页查询综合素质测评成绩 */
export const getComprehensiveEvaluationPage = (data?: ComprehensiveEvaluationQueryDTO) => {
  return http.request<ComprehensiveEvaluationPageVO>("post", "/api/score/comprehensive-evaluation/page", { data });
};

/** 获取横向综合素质测评成绩数据 */
export const getHorizontalComprehensiveEvaluation = (params?: {
  academicYear?: string;
  semesterId?: number;
  studentId?: string;
  currentPage?: number;
  pageSize?: number;
}) => {
  return http.request<HorizontalComprehensiveData>("get", "/api/score/comprehensive-evaluation/horizontal", { params });
};

/** 查询综合素质测评成绩数据（支持分页） */
export const getComprehensiveEvaluationList = (params?: any) => {
  return http.request<any>("get", "/api/score/comprehensive-evaluation/list", { params });
};

/** 导出综合素质测评成绩Excel */
export const exportComprehensiveEvaluation = (params: string) => {
  return http.request<Blob>("get", `/api/score/comprehensive-evaluation/export?${params}`, {
    responseType: "blob"
  });
};
