import { http } from "@/utils/http";

export interface QualityEvaluationVO {
  evaluationId?: number;
  studentId: string;
  studentName?: string;
  dormitory?: string;
  classCode?: string;
  className?: string;
  majorCode?: string;
  majorName?: string;
  collegeCode?: string;
  collegeName?: string;
  addScore?: number;
  reduceScore?: number;
  addScoreRemark?: string;
  reduceScoreRemark?: string;
  semesterId?: number;
  academicYear?: string;
  semesterNumber?: number;
  semesterName?: string;
  periodScore?: number;
  totalScore?: number;
  prevPeriodScore?: number;

  createTime?: string;
  updateTime?: string;
}

export interface QualityEvaluationQueryDTO {
  studentId?: string;
  studentName?: string;
  dormitory?: string;
  classCode?: string;
  majorCode?: string;
  collegeCode?: string;
  semesterId?: number;
  academicYear?: string;
  semesterNumber?: number;
  minTotalScore?: number;
  maxTotalScore?: number;
  current?: number;
  size?: number;
}

export interface QualityEvaluationPageVO {
  records: QualityEvaluationVO[];
  total: number;
  current: number;
  size: number;
}

/** 获取基本素质测评分页列表 */
export const getQualityEvaluationPage = (data?: QualityEvaluationQueryDTO) => {
  return http.request<QualityEvaluationPageVO>("post", "/api/score/quality-evaluation/page", { data });
};

/** 根据ID获取基本素质测评详情 */
export const getQualityEvaluationById = (evaluationId: number) => {
  return http.request<QualityEvaluationVO>("get", `/api/score/quality-evaluation/${evaluationId}`);
};

/** 保存基本素质测评 */
export const saveQualityEvaluation = (data: QualityEvaluationVO) => {
  return http.request("post", "/api/score/quality-evaluation", { data });
};

/** 更新基本素质测评 */
export const updateQualityEvaluation = (data: QualityEvaluationVO) => {
  return http.request("put", "/api/score/quality-evaluation", { data });
};

/** 删除基本素质测评 */
export const deleteQualityEvaluation = (evaluationId: number) => {
  return http.request("delete", `/api/score/quality-evaluation/${evaluationId}`);
};

/** 批量删除基本素质测评 */
export const batchDeleteQualityEvaluation = (evaluationIds: number[]) => {
  return http.request("post", "/api/score/quality-evaluation/batch-delete", { data: evaluationIds });
};



/** 根据学号和学期查询基本素质测评 */
export const getQualityEvaluationByStudentAndSemester = (studentId: string, semesterId: number) => {
  return http.request<QualityEvaluationVO>("get", `/api/score/quality-evaluation/by-student/${studentId}/${semesterId}`);
};

/** 导出基本素质测评成绩 */
export const exportQualityEvaluation = (params?: QualityEvaluationQueryDTO) => {
  return http.request<Blob>(
    "get",
    "/api/score/quality-evaluation/export",
    { params, responseType: "blob" }
  );
};

/** 下载导入模板 */
export const downloadQualityEvaluationTemplate = () => {
  return http.request<Blob>(
    "get",
    "/api/score/quality-evaluation/template",
    { responseType: "blob" }
  );
};

/** 导入基本素质测评成绩 */
export const importQualityEvaluation = (file: File) => {
  const formData = new FormData();
  formData.append("file", file);

  return http.request<string>(
    "post",
    "/api/score/quality-evaluation/import",
    {
      data: formData,
      headers: {
        "Content-Type": "multipart/form-data"
      }
    }
  );
};
