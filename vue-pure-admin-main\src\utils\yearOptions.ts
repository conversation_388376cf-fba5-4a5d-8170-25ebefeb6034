/**
 * 年份选项工具函数
 */

export interface YearOption {
  label: string;
  value: number;
}

/**
 * 生成年份选项（从今年开始的前四年）
 * @returns 年份选项数组
 */
export function getYearOptions(): number[] {
  const currentYear = new Date().getFullYear();
  const years: number[] = [];
  for (let i = 0; i < 4; i++) {
    years.push(currentYear - i);
  }
  return years;
}

/**
 * 生成带标签的年份选项（从今年开始的前四年）
 * @returns 带标签的年份选项数组
 */
export function getYearOptionsWithLabel(): YearOption[] {
  const currentYear = new Date().getFullYear();
  const years: YearOption[] = [];
  for (let i = 0; i < 4; i++) {
    const year = currentYear - i;
    years.push({
      label: `${year}年`,
      value: year
    });
  }
  return years;
}

/**
 * 获取当前年份
 * @returns 当前年份
 */
export function getCurrentYear(): number {
  return new Date().getFullYear();
}

/**
 * 生成学年选项（从今年开始的前四年）
 * @returns 学年选项数组
 */
export function getAcademicYearOptions(): string[] {
  const currentYear = new Date().getFullYear();
  const years: string[] = [];
  for (let i = 0; i < 4; i++) {
    const year = currentYear - i;
    years.push(`${year}-${year + 1}`);
  }
  return years;
}