import dayjs from "dayjs";
import editForm from "../form.vue";
import { message } from "@/utils/message";
import { addDialog } from "@/components/ReDialog";
import { type PaginationProps } from "@pureadmin/table";
import { reactive, ref, onMounted, h, toRaw } from "vue";
import { FormItemProps, QueryFormProps } from "./types";
import {
  getCollegeList,
  saveCollege,
  updateCollege,
  deleteCollege,
  type CollegeItem,
  type CollegeQueryParams
} from "@/api/basic/college";

export function useCollege() {
  const form = reactive<QueryFormProps>({
    collegeCode: "",
    collegeName: "",
    current: 1,
    size: 10
  });

  const dataList = ref<CollegeItem[]>([]);
  const loading = ref(true);

  // 排序参数
  const sortParams = reactive({
    sortField: "collegeName", // 默认按学院名称排序
    sortOrder: "asc" // asc 或 desc
  });

  const pagination = reactive<PaginationProps>({
    total: 0,
    pageSize: 10,
    currentPage: 1,
    background: true
  });

  const columns: TableColumnList = [
    {
      label: "序号",
      type: "index",
      width: 70
    },
    {
      label: "学院代码",
      prop: "collegeCode",
      minWidth: 120
    },
    {
      label: "学院名称",
      prop: "collegeName",
      minWidth: 150
    },
    {
      label: "学院描述",
      prop: "description",
      minWidth: 200,
      showOverflowTooltip: true
    },
    {
      label: "创建时间",
      minWidth: 180,
      prop: "createdAt",
      formatter: ({ createdAt }) =>
        dayjs(createdAt).format("YYYY-MM-DD HH:mm:ss")
    },
    {
      label: "操作",
      fixed: "right",
      width: 180,
      slot: "operation"
    }
  ];

  function handleDelete(row: CollegeItem) {
    deleteCollege({ id: row.id }).then(res => {
      if (res.success) {
        message(res.message, { type: "success" });
        onSearch();
      } else {
        message(res.message, { type: "error" });
      }
    }).catch(() => {
      message("网络错误，请稍后重试", { type: "error" });
    });
  }

  function handleSizeChange(val: number) {
    form.size = val;
    onSearch();
  }

  function handleCurrentChange(val: number) {
    form.current = val;
    onSearch();
  }

  function handleSelectionChange(val: CollegeItem[]) {
    // 处理选择变化
  }

  // 处理排序变化
  function handleSortChange({ prop, order }) {
    if (prop && order) {
      sortParams.sortField = prop;
      sortParams.sortOrder = order === "ascending" ? "asc" : "desc";
    } else {
      // 重置为默认排序
      sortParams.sortField = "collegeName";
      sortParams.sortOrder = "asc";
    }
    // 重置到第一页
    form.current = 1;
    pagination.currentPage = 1;
    onSearch();
  }

  async function onSearch() {
    loading.value = true;
    try {
      const searchParams = {
        ...toRaw(form),
        sortField: sortParams.sortField,
        sortOrder: sortParams.sortOrder
      };
      const response = await getCollegeList(searchParams);
      if (response.success && response.data) {
        dataList.value = response.data.list || [];
        pagination.total = response.data.total || 0;
        pagination.currentPage = response.data.pageNum || 1;
        pagination.pageSize = response.data.pageSize || 10;
      } else {
        message(response.message, { type: "error" });
      }
    } catch (error) {
      message("网络错误，请稍后重试", { type: "error" });
    } finally {
      loading.value = false;
    }
  }

  const resetForm = (formEl: any) => {
    if (!formEl) return;
    formEl.resetFields();
    onSearch();
  };

  function openDialog(title = "新增", row?: CollegeItem) {
    const formRef = ref();

    addDialog({
      title: `${title}学院`,
      props: {
        formInline: {
          id: row?.id ?? null,
          collegeCode: row?.collegeCode ?? "",
          collegeName: row?.collegeName ?? "",
          description: row?.description ?? ""
        }
      },
      width: "40%",
      draggable: true,
      fullscreenIcon: true,
      closeOnClickModal: false,
      contentRenderer: () => h(editForm, { ref: formRef }),
      beforeSure: (done, { options }) => {
        const FormRef = formRef.value?.getRef();
        const curData = options.props.formInline as CollegeItem;

        function chores(successMessage: string) {
          message(successMessage, { type: "success" });
          done(); // 关闭弹框
          onSearch(); // 刷新表格数据
        }

        FormRef?.validate((valid: boolean) => {
          if (valid) {
            // 表单规则校验通过
            if (title === "新增") {
              saveCollege(curData).then((res: any) => {
                if (res.success) {
                  chores(res.message);
                } else {
                  message(res.message, { type: "error" });
                }
              }).catch(() => {
                message("网络错误，请稍后重试", { type: "error" });
              });
            } else {
              updateCollege(curData).then((res: any) => {
                if (res.success) {
                  chores(res.message);
                } else {
                  message(res.message, { type: "error" });
                }
              }).catch(() => {
                message("网络错误，请稍后重试", { type: "error" });
              });
            }
          }
        });
      }
    });
  }

  onMounted(() => {
    onSearch();
  });

  return {
    form,
    loading,
    columns,
    dataList,
    pagination,
    onSearch,
    resetForm,
    openDialog,
    handleDelete,
    handleSizeChange,
    handleCurrentChange,
    handleSelectionChange,
    handleSortChange
  };
}
