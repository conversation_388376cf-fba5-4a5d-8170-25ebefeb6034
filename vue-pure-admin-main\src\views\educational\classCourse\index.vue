<template>
  <div class="main">
    <!-- 班级列表视图 -->
    <ClassSelector
      v-if="currentView === 'classes'"
      title="班级课程分配管理"
      @select="handleViewClassCourses"
    >
      <template #buttons>
        <el-button
          type="success"
          :icon="useRenderIcon('ep:plus')"
          @click="handleBatchAssign"
        >
          批量分配课程
        </el-button>
      </template>

      <template #operation="{ row, size }">
        <el-button
          class="reset-margin"
          link
          type="primary"
          :size="size"
          :icon="useRenderIcon(View)"
          @click="handleViewClassCourses(row)"
        >
          查看课程
        </el-button>
      </template>
    </ClassSelector>

    <!-- 班级课程详情视图 -->
    <ClassCourseList
      v-else-if="currentView === 'courses'"
      :selectedClass="selectedClass"
      @back="handleBackToClassList"
    />

    <!-- 批量分配对话框 -->
    <BatchAssignDialog
      v-model:visible="batchAssignVisible"
      @success="handleBatchAssignSuccess"
    />
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
import { ClassSelector } from "@/components/ReClassSelector";
import ClassCourseList from "./components/ClassCourseList.vue";
import BatchAssignDialog from "./components/BatchAssignDialog.vue";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";

import View from "~icons/ep/view";

defineOptions({
  name: "ClassCourse"
});

// 当前视图状态：'classes' | 'courses'
const currentView = ref<'classes' | 'courses'>('classes');
const selectedClass = ref<any>(null);

// 批量分配对话框
const batchAssignVisible = ref(false);

/** 查看班级课程 */
function handleViewClassCourses(classRow: any) {
  selectedClass.value = classRow;
  currentView.value = 'courses';
}



/** 批量分配课程 */
function handleBatchAssign() {
  batchAssignVisible.value = true;
}

/** 批量分配成功回调 */
function handleBatchAssignSuccess() {
  // 可以在这里刷新班级列表或显示成功消息
  ElMessage.success("批量分配成功");
}

/** 返回班级列表 */
function handleBackToClassList() {
  currentView.value = 'classes';
  selectedClass.value = null;
}
</script>

<style scoped>
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>
