import dayjs from "dayjs";
import editForm from "../form.vue";
import { message } from "@/utils/message";
import { addDialog } from "@/components/ReDialog";
import { type PaginationProps, type TableColumnList } from "@pureadmin/table";
import { reactive, ref, onMounted, h, toRaw } from "vue";
import { FormItemProps } from "./types";
import {
  getCoursePage,
  saveCourse,
  updateCourse,
  deleteCourse,
  batchDeleteCourses,
  type CourseVO,
  type CourseQueryDTO
} from "@/api/educational/course";
import { getAllColleges, type CollegeItem } from "@/api/basic/college";

export function useCourse() {
  const form = reactive({
    courseCode: "",
    courseName: "",
    courseType: "",
    collegeCode: ""
  });

  const dataList = ref<CourseVO[]>([]);
  const loading = ref(true);
  const collegeOptions = ref<CollegeItem[]>([]);

  // 课程类型选项
  const courseTypeOptions = [
    { label: "必修", value: "必修" },
    { label: "选修", value: "选修" },
    { label: "实践", value: "实践" },
    { label: "通识", value: "通识" }
  ];

  const pagination = reactive<PaginationProps>({
    total: 0,
    pageSize: 10,
    currentPage: 1,
    background: true
  });

  const columns: TableColumnList = [
    {
      label: "序号",
      type: "index",
      width: 70
    },
    {
      label: "课程代码",
      prop: "courseCode",
      minWidth: 120
    },
    {
      label: "课程名称",
      prop: "courseName",
      minWidth: 150
    },
    {
      label: "学分",
      prop: "credits",
      width: 80
    },
    {
      label: "课程类型",
      prop: "courseType",
      width: 100
    },
    {
      label: "所属学院",
      prop: "collegeName",
      minWidth: 120
    },

    {
      label: "创建时间",
      prop: "createdAt",
      minWidth: 180,
      formatter: ({ createdAt }) =>
        dayjs(createdAt).format("YYYY-MM-DD HH:mm:ss")
    },
    {
      label: "操作",
      fixed: "right",
      width: 180,
      slot: "operation"
    }
  ];

  function handleSelectionChange(val: CourseVO[]) {
    // 处理选择变化
  }

  async function onSearch() {
    loading.value = true;
    try {
      const query: CourseQueryDTO = {
        courseCode: form.courseCode || undefined,
        courseName: form.courseName || undefined,
        courseType: form.courseType || undefined,
        collegeCode: form.collegeCode || undefined,
        current: pagination.currentPage,
        size: pagination.pageSize
      };

      const response = await getCoursePage(query);

      if (response.success && response.data) {
        dataList.value = response.data.records || [];
        pagination.total = response.data.total || 0;
      } else {
        dataList.value = [];
        pagination.total = 0;
        message(response?.message || "查询失败", { type: "error" });
      }
    } catch (error) {
      message("网络错误，请稍后重试", { type: "error" });
      dataList.value = [];
      pagination.total = 0;
    } finally {
      loading.value = false;
    }
  }

  const resetForm = (formEl: any) => {
    if (!formEl) return;
    formEl.resetFields();
    onSearch();
  };

  // 加载学院选项
  async function loadCollegeOptions() {
    try {
      const response = await getAllColleges();
      if (response.success && response.data) {
        collegeOptions.value = response.data;
      }
    } catch (error) {
      console.error("加载学院选项失败:", error);
    }
  }

  // 学院变化处理
  function onCollegeChange(collegeCode: string) {
    // 学院变化时的处理逻辑（如果需要的话）
  }

  function openDialog(title = "新增", row?: CourseVO) {
    const formRef = ref();

    addDialog({
      title: `${title}课程`,
      props: {
        formInline: {
          id: row?.id ?? null,
          courseCode: row?.courseCode ?? "",
          courseName: row?.courseName ?? "",
          credits: row?.credits ?? 0,
          courseType: row?.courseType ?? "",
          collegeCode: row?.collegeCode ?? "",
          description: row?.description ?? ""
        }
      },
      width: "46%",
      draggable: true,
      fullscreenIcon: true,
      closeOnClickModal: false,
      contentRenderer: () => h(editForm, { ref: formRef }),
      beforeSure: (done, { options }) => {
        const FormRef = formRef.value?.getRef();
        const curData = options.props.formInline as CourseVO;

        function chores(successMessage: string) {
          message(successMessage, { type: "success" });
          done(); // 关闭弹框
          onSearch(); // 刷新表格数据
        }

        FormRef?.validate((valid: boolean) => {
          if (valid) {
            // 表单规则校验通过
            if (title === "新增") {
              saveCourse(curData).then((res: any) => {
                if (res.success) {
                  chores(res.message);
                } else {
                  message(res.message, { type: "error" });
                }
              }).catch(() => {
                message("网络错误，请稍后重试", { type: "error" });
              });
            } else {
              updateCourse(curData).then((res: any) => {
                if (res.success) {
                  chores(res.message);
                } else {
                  message(res.message, { type: "error" });
                }
              }).catch(() => {
                message("网络错误，请稍后重试", { type: "error" });
              });
            }
          }
        });
      }
    });
  }

  async function handleDelete(row: CourseVO) {
    try {
      const res = await deleteCourse(row.id);
      if (res.success) {
        message(res.message, { type: "success" });
        onSearch();
      } else {
        message(res.message, { type: "error" });
      }
    } catch (error) {
      message("删除失败", { type: "error" });
    }
  }

  function handleSizeChange(val: number) {
    pagination.pageSize = val;
    onSearch();
  }

  function handleCurrentChange(val: number) {
    pagination.currentPage = val;
    onSearch();
  }

  onMounted(() => {
    onSearch();
    loadCollegeOptions();
  });

  return {
    form,
    loading,
    columns,
    dataList,
    pagination,
    collegeOptions,
    courseTypeOptions,
    onSearch,
    resetForm,
    openDialog,
    handleDelete,
    handleSizeChange,
    handleCurrentChange,
    handleSelectionChange,
    onCollegeChange
  };
}
