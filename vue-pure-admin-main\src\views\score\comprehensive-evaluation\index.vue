<template>
  <div class="main-content">
    <!-- 表格工具栏 -->
    <PureTableBar
      title="综合素质测评成绩"
      :columns="columns"
      @refresh="handleRefresh"
    >
      <template #buttons>
        <el-space>
          <!-- 搜索框 -->
          <el-select
            v-model="searchForm.academicYear"
            placeholder="请选择学年"
            clearable
            style="width: 150px"
          >
            <el-option
              v-for="year in academicYears"
              :key="year"
              :label="year"
              :value="year"
            />
          </el-select>
          <el-input
            v-model="searchForm.className"
            placeholder="班级名称"
            clearable
            @input="onSearch"
            style="width: 150px"
          />
          <el-input
            v-model="searchForm.studentId"
            placeholder="学号"
            clearable
            @input="onSearch"
            style="width: 150px"
          />
          <el-button type="primary" @click="onSearch">
            搜索
          </el-button>
          <el-button @click="resetSearch">
            重置
          </el-button>

          <!-- 操作按钮 -->
          <el-button
            type="success"
            @click="handleExport"
            :loading="exportLoading"
          >
            导出Excel
          </el-button>
        </el-space>
      </template>
      <template v-slot="{ size, dynamicColumns }">
        <pure-table
          ref="tableRef"
          adaptive
          :adaptiveConfig="{ offsetBottom: 108 }"
          align-whole="center"
          table-layout="auto"
          :loading="queryLoading"
          :size="size"
          :data="tableData"
          :columns="dynamicColumns"
          :pagination="pagination"
          :paginationSmall="size === 'small'"
          :header-cell-style="{
            background: 'var(--el-fill-color-light)',
            color: 'var(--el-text-color-primary)'
          }"
          @page-size-change="handlePageSizeChange"
          @page-current-change="handlePageChange"
        >
          <template #empty>
            <el-empty description="暂无数据" />
          </template>
        </pure-table>
      </template>
    </PureTableBar>

  </div>
</template>

<script setup lang="ts">
import { PureTableBar } from "@/components/RePureTableBar";
import { useComprehensiveEvaluation } from "./utils/hook";

defineOptions({
  name: "ComprehensiveEvaluation"
});

const {
  // 响应式数据
  searchFormRef,
  tableRef,
  searchForm,
  academicYears,
  tableData,
  queryLoading,
  exportLoading,
  pagination,
  columns,

  // 方法
  handleQuery,
  resetForm,
  onSearch,
  resetSearch,
  handleRefresh,
  handlePageSizeChange,
  handlePageChange,
  handleExport,

  // 图标
  useRenderIcon
} = useComprehensiveEvaluation();


</script>
