import { ref, reactive, onMounted } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import { getAcademicYearOptions } from "@/utils/yearOptions";
import type { PaginationProps } from "@pureadmin/table";
import {
  getComprehensiveEvaluationList,
  exportComprehensiveEvaluation
} from "@/api/score/comprehensive-evaluation";
import type {
  QueryFormProps,
  ComprehensiveEvaluationItem
} from "./types";

export function useComprehensiveEvaluation() {
  /** 响应式数据 */
  const searchFormRef = ref();
  const tableRef = ref();
  const searchForm = ref<QueryFormProps>({
    academicYear: "",
    className: "",
    studentId: ""
  });

  const academicYears = ref<string[]>([]);
  const tableData = ref<ComprehensiveEvaluationItem[]>([]);
  const queryLoading = ref(false);
  const exportLoading = ref(false);

  /** 分页配置 */
  const pagination = reactive<PaginationProps>({
    total: 0,
    currentPage: 1,
    pageSize: 20,
    pageSizes: [10, 20, 50, 100],
    layout: "total, sizes, prev, pager, next, jumper",
    background: true
  });

  /** 表格列配置 */
  const columns: TableColumnList = [
    {
      type: "index",
      label: "序号",
      width: 60
    },
    {
      prop: "studentId",
      label: "学号",
      width: 120
    },
    {
      prop: "studentName",
      label: "姓名",
      width: 100
    },
    {
      prop: "className",
      label: "班级",
      width: 120
    },
    {
      prop: "majorName",
      label: "专业",
      width: 150
    },
    {
      prop: "academicYear",
      label: "学年",
      width: 100
    },
    {
      prop: "academicScore",
      label: "学业成绩(70%)",
      width: 120,
      cellRenderer: ({ row }) => (
        <span class="text-blue-600">{(row.academicScore || 0).toFixed(2)}</span>
      )
    },
    {
      prop: "qualityScore",
      label: "基本素质测评成绩(30%)",
      width: 200,
      cellRenderer: ({ row }) => (
        <span class="text-green-600">{(row.qualityScore || 0).toFixed(2)}</span>
      )
    },
    {
      prop: "comprehensiveScore",
      label: "综合素质测评成绩",
      width: 140,
      cellRenderer: ({ row }) => (
        <span class="text-red-600 font-bold">{(row.comprehensiveScore || 0).toFixed(2)}</span>
      )
    }
  ];

  /** 初始化数据 */
  onMounted(() => {
    loadAcademicYears();
  });

  /** 加载学年数据 */
  const loadAcademicYears = () => {
    try {
      academicYears.value = getAcademicYearOptions();
    } catch (error) {
      console.error("加载学年数据失败:", error);
    }
  };

  /** 查询数据 */
  const handleQuery = async (page = 1) => {
    queryLoading.value = true;
    try {
      pagination.currentPage = page;

      const params = {
        ...searchForm.value,
        currentPage: pagination.currentPage,
        pageSize: pagination.pageSize
      };

      const { data } = await getComprehensiveEvaluationList(params);

      tableData.value = data.records || [];
      pagination.total = data.total || 0;
      pagination.currentPage = data.current || 1;
      pagination.pageSize = data.size || 20;

      if (pagination.total === 0) {
        ElMessage.warning("没有找到符合条件的数据");
      } else {
        ElMessage.success(`查询成功，共 ${pagination.total} 条数据`);
      }
    } catch (error) {
      console.error("查询数据失败:", error);
      ElMessage.error("查询数据失败");
    } finally {
      queryLoading.value = false;
    }
  };

  /** 重置表单 */
  const resetForm = () => {
    searchFormRef.value?.resetFields();
    searchForm.value = {
      academicYear: "",
      className: "",
      studentId: ""
    };
    tableData.value = [];
    pagination.total = 0;
    pagination.currentPage = 1;
  };

  /** 搜索方法 */
  const onSearch = () => {
    handleQuery(1);
  };

  /** 重置搜索 */
  const resetSearch = () => {
    resetForm();
  };

  /** 刷新方法 */
  const handleRefresh = () => {
    handleQuery();
  };

  /** 分页大小改变 */
  const handlePageSizeChange = (val: number) => {
    pagination.pageSize = val;
    pagination.currentPage = 1;
    handleQuery(1);
  };

  /** 当前页改变 */
  const handlePageChange = (val: number) => {
    pagination.currentPage = val;
    handleQuery(val);
  };

  /** 导出Excel */
  const handleExport = async () => {
    if (tableData.value.length === 0) {
      ElMessage.warning("没有数据可以导出");
      return;
    }

    try {
      await ElMessageBox.confirm(
        "确认导出当前查询条件下的所有综合素质测评成绩数据？",
        "导出确认",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }
      );

      exportLoading.value = true;

      const params = new URLSearchParams();
      if (searchForm.value.academicYear) {
        params.append("academicYear", searchForm.value.academicYear);
      }
      if (searchForm.value.className) {
        params.append("className", searchForm.value.className);
      }
      if (searchForm.value.studentId) {
        params.append("studentId", searchForm.value.studentId);
      }

      const response = await exportComprehensiveEvaluation(params.toString());

      // 创建下载链接
      const blob = new Blob([response], {
        type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
      });
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.href = url;

      // 生成文件名
      const now = new Date();
      const timestamp = now.toISOString().slice(0, 19).replace(/[:-]/g, "");
      link.download = `综合素质测评成绩_${timestamp}.xlsx`;

      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);

      ElMessage.success("导出成功");
    } catch (error) {
      if (error !== "cancel") {
        console.error("导出失败:", error);
        ElMessage.error("导出失败");
      }
    } finally {
      exportLoading.value = false;
    }
  };

  return {
    /** 响应式数据 */
    searchFormRef,
    tableRef,
    searchForm,
    academicYears,
    tableData,
    queryLoading,
    exportLoading,
    pagination,
    columns,

    /** 方法 */
    handleQuery,
    resetForm,
    onSearch,
    resetSearch,
    handleRefresh,
    handlePageSizeChange,
    handlePageChange,
    handleExport,

    /** 图标 */
    useRenderIcon
  };
}
