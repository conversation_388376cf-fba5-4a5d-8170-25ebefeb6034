/** 综合素质测评成绩项 */
export interface ComprehensiveEvaluationItem {
  studentId: string;
  studentName: string;
  className: string;
  majorName: string;
  academicYear: string;
  academicScore: number;
  qualityScore: number;
  comprehensiveScore: number;
  createTime?: string;
  updateTime?: string;
}

/** 查询表单 */
export interface QueryFormProps {
  academicYear: string;
  className: string;
  studentId: string;
}

/** 综合素质测评查询DTO */
export interface ComprehensiveEvaluationQueryDTO {
  academicYear?: string;
  studentId?: string;
  studentName?: string;
  className?: string;
  majorCode?: string;
  collegeCode?: string;
  minAcademicScore?: number;
  maxAcademicScore?: number;
  minQualityScore?: number;
  maxQualityScore?: number;
  minComprehensiveScore?: number;
  maxComprehensiveScore?: number;
  currentPage?: number;
  pageSize?: number;
  limit?: number;
}


