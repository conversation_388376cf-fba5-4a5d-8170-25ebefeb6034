<template>
  <el-dialog
    v-model="dialogVisible"
    :title="isEdit ? '编辑基本素质测评' : '新增基本素质测评'"
    width="800px"
    :before-close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="120px"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="学号" prop="studentId">
            <el-input
              v-model="form.studentId"
              placeholder="请输入学号"
              @blur="handleStudentIdBlur"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="学生姓名">
            <el-input
              v-model="form.studentName"
              placeholder="自动获取"
              readonly
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="班级">
            <el-input
              v-model="form.className"
              placeholder="自动获取"
              readonly
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="学期" prop="semesterId">
            <el-select
              v-model="form.semesterId"
              placeholder="请选择学期"
              style="width: 100%"
            >
              <el-option
                v-for="semester in semesterOptions"
                :key="semester.id"
                :label="semester.semesterName"
                :value="semester.id"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="基础分" prop="periodScore">
            <el-input-number
              v-model="form.periodScore"
              :min="0"
              :max="100"
              :precision="2"
              style="width: 100%"
              readonly
              placeholder="自动计算"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="总分">
            <el-input-number
              v-model="form.totalScore"
              :precision="2"
              style="width: 100%"
              readonly
              placeholder="自动计算"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="加分说明">
        <el-input
          v-model="form.addScoreRemark"
          type="textarea"
          :rows="3"
          placeholder="请输入加分说明"
        />
      </el-form-item>

      <el-form-item label="扣分说明">
        <el-input
          v-model="form.reduceScoreRemark"
          type="textarea"
          :rows="3"
          placeholder="请输入扣分说明"
        />
      </el-form-item>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="加分项">
            <el-input-number
              v-model="form.addScore"
              :precision="2"
              style="width: 100%"
              readonly
              placeholder="自动计算"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="扣分项">
            <el-input-number
              v-model="form.reduceScore"
              :precision="2"
              style="width: 100%"
              readonly
              placeholder="自动计算"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitLoading">
          确定
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch, computed, onMounted } from "vue";
import { ElMessage, type FormInstance, type FormRules } from "element-plus";
import {
  saveQualityEvaluation,
  updateQualityEvaluation,
  type QualityEvaluationVO
} from "@/api/score/qualityEvaluation";
import { getStudentByStudentId } from "@/api/student/students";
import { getAllSemesters, type SemesterItem } from "@/api/basic/semester";
import { getClassesByCode } from "@/api/basic/classes";

interface Props {
  visible?: boolean;
  formData?: QualityEvaluationVO;
  isEdit?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  formData: () => ({} as QualityEvaluationVO),
  isEdit: false
});

const emit = defineEmits<{
  "update:visible": [value: boolean];
  success: [];
}>();

// 响应式数据
const formRef = ref<FormInstance>();
const submitLoading = ref(false);

// 对话框显示状态
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit("update:visible", value)
});

// 表单数据
const form = reactive<QualityEvaluationVO>({
  studentId: "",
  studentName: "",
  className: "",
  semesterId: undefined,
  periodScore: 60, // 默认基础分
  addScore: 0,
  reduceScore: 0,
  addScoreRemark: "",
  reduceScoreRemark: "",
  totalScore: 80
});

// 学期选项
const semesterOptions = ref<SemesterItem[]>([]);

// 加载学期数据
const loadSemesters = async () => {
  try {
    const { data } = await getAllSemesters();
    semesterOptions.value = data || [];
  } catch (error) {
    console.error("加载学期数据失败:", error);
    ElMessage.error("加载学期数据失败");
  }
};

// 表单验证规则
const rules: FormRules = {
  studentId: [
    { required: true, message: "请输入学号", trigger: "blur" }
  ],
  semesterId: [
    { required: true, message: "请选择学期", trigger: "change" }
  ],
  periodScore: [
    { required: true, message: "请输入基础分", trigger: "blur" }
  ]
};

// 重置表单
const resetForm = () => {
  Object.assign(form, {
    studentId: "",
    studentName: "",
    className: "",
    semesterId: undefined,
    periodScore: 60,
    addScore: 0,
    reduceScore: 0,
    addScoreRemark: "",
    reduceScoreRemark: "",
    totalScore: 80
  });
};

// 监听表单数据变化
watch(
  () => props.formData,
  (newData) => {
    if (newData && Object.keys(newData).length > 0) {
      Object.assign(form, newData);
    } else {
      resetForm();
    }
  },
  { immediate: true, deep: true }
);

// 监听学期变化，自动设置基础分
watch(
  () => form.semesterId,
  async (newSemesterId) => {
    if (newSemesterId && form.studentId) {
      await updatePeriodScore();
    }
  }
);

// 更新基础分
const updatePeriodScore = async () => {
  // 这里可以根据学期和学生信息计算基础分
  // 暂时使用默认值
  form.periodScore = 60;
  calculateTotalScore();
};

// 计算总分
const calculateTotalScore = () => {
  form.totalScore = form.periodScore + (form.addScore || 0) - (form.reduceScore || 0);
};

// 学号失焦事件 - 获取学生信息
const handleStudentIdBlur = async () => {
  if (!form.studentId) return;

  try {
    const { data } = await getStudentByStudentId(form.studentId);
    if (data) {
      // 注意：API返回的字段是 name，不是 studentName
      form.studentName = data.name;
      // 设置班级代码
      if (data.classCode) {
        form.classCode = data.classCode;
      }

      // 如果className为空，通过classCode获取班级名称
      if (data.className) {
        form.className = data.className;
        ElMessage.success("学生信息获取成功");
      } else if (data.classCode) {
        try {
          const classResult = await getClassesByCode(data.classCode);
          if (classResult.data) {
            form.className = classResult.data.className;
          } else {
            form.className = data.classCode; // 如果获取不到班级名称，使用班级代码
          }
          ElMessage.success("学生信息获取成功");
        } catch (classError) {
          form.className = data.classCode; // 如果获取班级信息失败，使用班级代码
          ElMessage.success("学生信息获取成功");
        }
      } else {
        form.className = "";
        ElMessage.success("学生信息获取成功");
      }
    }
  } catch (error) {
    ElMessage.warning("获取学生信息失败，请检查学号是否正确");
    // 清空相关字段
    form.studentName = "";
    form.className = "";
    form.classCode = "";
  }
};




// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return;

  try {
    await formRef.value.validate();
    submitLoading.value = true;

    if (props.isEdit) {
      await updateQualityEvaluation(form);
      ElMessage.success("更新成功");
    } else {
      await saveQualityEvaluation(form);
      ElMessage.success("新增成功");
    }

    emit("success");
    handleClose();
  } catch (error) {
    ElMessage.error("操作失败");
  } finally {
    submitLoading.value = false;
  }
};

// 关闭对话框
const handleClose = () => {
  formRef.value?.resetFields();
  resetForm();
  emit("update:visible", false);
};

// 组件挂载时加载数据
onMounted(() => {
  loadSemesters();
});
</script>

<style scoped>
.dialog-footer {
  text-align: right;
}
</style>
