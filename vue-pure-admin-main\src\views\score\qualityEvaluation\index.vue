<template>
  <div class="main">
    <el-form
      ref="formRef"
      :inline="true"
      :model="form"
      class="search-form bg-bg_color w-[99/100] pl-8 pt-[12px]"
    >
      <el-form-item label="学生姓名：" prop="studentName">
        <el-input
          v-model="form.studentName"
          placeholder="请输入学生姓名"
          clearable
          class="!w-[180px]"
        />
      </el-form-item>
      <el-form-item label="学号：" prop="studentId">
        <el-input
          v-model="form.studentId"
          placeholder="请输入学号"
          clearable
          class="!w-[180px]"
        />
      </el-form-item>
      <el-form-item label="宿舍号：" prop="dormitory">
        <el-input
          v-model="form.dormitory"
          placeholder="请输入宿舍号"
          clearable
          class="!w-[180px]"
        />
      </el-form-item>
      <el-form-item label="班级：" prop="classCode">
        <el-input
          v-model="form.classCode"
          placeholder="请输入班级代码"
          clearable
          class="!w-[180px]"
        />
      </el-form-item>
      <el-form-item label="学期：" prop="semesterId">
        <el-select
          v-model="form.semesterId"
          placeholder="请选择学期"
          clearable
          class="!w-[180px]"
        >
          <el-option
            v-for="semester in semesterOptions"
            :key="semester.id"
            :label="semester.semesterName"
            :value="semester.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          :icon="useRenderIcon(Search)"
          :loading="loading"
          @click="onSearch"
        >
          搜索
        </el-button>
        <el-button :icon="useRenderIcon(Refresh)" @click="resetForm">
          重置
        </el-button>
      </el-form-item>
    </el-form>

    <PureTableBar title="基本素质测评列表" :columns="columns" @refresh="onSearch">
      <template #buttons>
        <el-button
          type="primary"
          :icon="useRenderIcon(AddFill)"
          @click="openDialog()"
        >
          新增基本素质测评
        </el-button>
        <el-button
          type="danger"
          :icon="useRenderIcon(Delete)"
          :disabled="!selectedNum"
          @click="onbatchDel"
        >
          批量删除({{ selectedNum }})
        </el-button>
        <el-button
          type="success"
          @click="handleExport"
        >
          导出
        </el-button>
        <el-button
          type="warning"
          @click="handleDownloadTemplate"
        >
          下载模板
        </el-button>
        <el-upload
          :show-file-list="false"
          :before-upload="handleImport"
          accept=".xlsx,.xls"
          style="display: inline-block; margin-left: 10px"
        >
          <el-button type="info">
            导入
          </el-button>
        </el-upload>
      </template>
      <template v-slot="{ size, dynamicColumns }">
        <pure-table
          ref="tableRef"
          border
          adaptive
          :loading="loading"
          :size="size"
          :data="dataList"
          :columns="dynamicColumns"
          :pagination="pagination"
          :paginationSmall="size === 'small' ? true : false"
          :header-cell-style="{
            background: 'var(--el-fill-color-light)',
            color: 'var(--el-text-color-primary)'
          }"
          @selection-change="handleSelectionChange"
          @page-size-change="handleSizeChange"
          @page-current-change="handleCurrentChange"
        >
          <template #operation="{ row }">
            <el-button
              class="reset-margin"
              link
              type="primary"
              :size="size"
              :icon="useRenderIcon(EditPen)"
              @click="handleUpdate(row)"
            >
              修改
            </el-button>
            <el-popconfirm
              :title="`是否确认删除学号为${row.studentId}的这条数据`"
              @confirm="handleDelete(row)"
            >
              <template #reference>
                <el-button
                  class="reset-margin"
                  link
                  type="primary"
                  :size="size"
                  :icon="useRenderIcon(Delete)"
                >
                  删除
                </el-button>
              </template>
            </el-popconfirm>
          </template>
        </pure-table>
      </template>
    </PureTableBar>

    <!-- 新增/编辑表单弹窗 -->
    <QualityEvaluationForm
      v-model:visible="dialogVisible"
      :form-data="currentFormData"
      :is-edit="isEdit"
      @success="onSearch"
    />
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
import { useQualityEvaluation } from "./utils/hook";
import { PureTableBar } from "@/components/RePureTableBar";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import QualityEvaluationForm from "./form/index.vue";

import Delete from "~icons/ep/delete";
import EditPen from "~icons/ep/edit-pen";
import Search from "~icons/ep/search";
import Refresh from "~icons/ep/refresh";
import AddFill from "~icons/ri/add-circle-line";

defineOptions({
  name: "QualityEvaluation"
});

const formRef = ref();
const tableRef = ref();

const {
  form,
  loading,
  columns,
  dataList,
  selectedNum,
  pagination,
  semesterOptions,
  deviceDetection,
  dialogVisible,
  currentFormData,
  isEdit,
  onSearch,
  resetForm,
  onbatchDel,
  openDialog,
  handleUpdate,
  handleDelete,
  handleExport,
  handleDownloadTemplate,
  handleImport,
  handleSizeChange,
  onSelectionCancel,
  handleCurrentChange,
  handleSelectionChange
} = useQualityEvaluation(tableRef);
</script>

<style scoped>


/* 备注内容样式 */

</style>

