import dayjs from "dayjs";
import { ElMessage, ElMessageBox } from "element-plus";
import type { PaginationProps } from "@pureadmin/table";
import { deviceDetection } from "@pureadmin/utils";
import {
  getQualityEvaluationPage,
  deleteQualityEvaluation,
  batchDeleteQualityEvaluation,
  exportQualityEvaluation,
  downloadQualityEvaluationTemplate,
  importQualityEvaluation,
  type QualityEvaluationVO,
  type QualityEvaluationQueryDTO
} from "@/api/score/qualityEvaluation";
import { getAllSemesters, type SemesterItem } from "@/api/basic/semester";
import { type Ref, reactive, ref, onMounted, h } from "vue";

export function useQualityEvaluation(tableRef: Ref) {

  // 响应式数据
  const loading = ref(false);
  const dataList = ref<QualityEvaluationVO[]>([]);
  const multipleSelection = ref<QualityEvaluationVO[]>([]);
  const selectedNum = ref(0);

  // 搜索表单
  const form = reactive<QualityEvaluationQueryDTO>({
    studentName: "",
    studentId: "",
    dormitory: "",
    classCode: "",
    semesterId: undefined
  });

  // 分页
  const pagination = reactive<PaginationProps>({
    total: 0,
    pageSize: 20,
    currentPage: 1,
    background: true
  });

  // 学期选项
  const semesterOptions = ref<SemesterItem[]>([]);

  // 弹窗控制
  const dialogVisible = ref(false);
  const currentFormData = ref<QualityEvaluationVO>({} as QualityEvaluationVO);
  const isEdit = ref(false);

  // 表格列定义
  const columns: TableColumnList = [
    {
      label: "勾选列",
      type: "selection",
      width: 55,
      align: "center"
    },
    {
      label: "序号",
      type: "index",
      width: 70,
      align: "center"
    },
    {
      label: "学号",
      prop: "studentId",
      minWidth: 120,
      align: "center"
    },
    {
      label: "学生姓名",
      prop: "studentName",
      minWidth: 100,
      align: "center"
    },
    {
      label: "班级",
      prop: "className",
      minWidth: 120,
      align: "center"
    },
    {
      label: "宿舍号",
      prop: "dormitory",
      minWidth: 100,
      align: "center"
    },
    {
      label: "学期",
      prop: "semesterName",
      minWidth: 120,
      align: "center"
    },
    {
      label: "基础分",
      prop: "periodScore",
      minWidth: 100,
      align: "center"
    },
    {
      label: "加分",
      prop: "addScore",
      minWidth: 80,
      align: "center"
    },
    {
      label: "扣分",
      prop: "reduceScore",
      minWidth: 80,
      align: "center"
    },
    {
      label: "总分",
      prop: "totalScore",
      minWidth: 100,
      align: "center"
    },
    {
      label: "加分说明",
      prop: "addScoreRemark",
      minWidth: 200,
      cellRenderer: ({ row }) => {
        const content = row.addScoreRemark || "-";
        if (content === "-") {
          return h("div", { class: "remark-content" }, "-");
        }
        // 将换行符分割成数组，每行作为一个 div
        const lines = content.split('\n').filter((line: string) => line.trim());
        return h("div", { class: "remark-content" },
          lines.map((line: string) => h("div", { class: "remark-line" }, line.trim()))
        );
      }
    },
    {
      label: "扣分说明",
      prop: "reduceScoreRemark",
      minWidth: 200,
      cellRenderer: ({ row }) => {
        const content = row.reduceScoreRemark || "-";
        if (content === "-") {
          return h("div", { class: "remark-content" }, "-");
        }
        // 将换行符分割成数组，每行作为一个 div
        const lines = content.split('\n').filter((line: string) => line.trim());
        return h("div", { class: "remark-content" },
          lines.map((line: string) => h("div", { class: "remark-line" }, line.trim()))
        );
      }
    },
    {
      label: "创建时间",
      prop: "createTime",
      minWidth: 180,
      align: "center",
      formatter: ({ createTime }) =>
        dayjs(createTime).format("YYYY-MM-DD HH:mm:ss")
    },
    {
      label: "操作",
      fixed: "right",
      width: 180,
      slot: "operation"
    }
  ];

  // 加载学期数据
  const loadSemesters = async () => {
    try {
      const response = await getAllSemesters();
      // 直接使用 response，因为 API 可能直接返回数组
      semesterOptions.value = Array.isArray(response) ? response : (response?.data || []);
    } catch (error) {
      console.error("加载学期数据失败:", error);
      ElMessage.error("加载学期数据失败");
      semesterOptions.value = [];
    }
  };

  // 获取列表数据
  const onSearch = async () => {
    loading.value = true;
    try {
      const params: QualityEvaluationQueryDTO = {
        ...form,
        current: pagination.currentPage,
        size: pagination.pageSize
      };
      const response = await getQualityEvaluationPage(params);
      // 正确访问响应数据结构，使用类型断言处理API响应格式
      const apiResponse = response as any;
      dataList.value = apiResponse?.data?.records || [];
      pagination.total = apiResponse?.data?.total || 0;
    } catch (error) {
      console.error("获取数据失败:", error);
      ElMessage.error("获取数据失败");
      dataList.value = [];
      pagination.total = 0;
    } finally {
      loading.value = false;
    }
  };

  // 重置搜索表单
  const resetForm = () => {
    Object.assign(form, {
      studentName: "",
      studentId: "",
      dormitory: "",
      classCode: "",
      semesterId: undefined
    });
    pagination.currentPage = 1;
    onSearch();
  };

  // 分页相关
  const handleSizeChange = (val: number) => {
    pagination.pageSize = val;
    onSearch();
  };

  const handleCurrentChange = (val: number) => {
    pagination.currentPage = val;
    onSearch();
  };

  // 表格选择相关
  const handleSelectionChange = (val: QualityEvaluationVO[]) => {
    multipleSelection.value = val;
    selectedNum.value = val.length;
  };

  const onSelectionCancel = () => {
    selectedNum.value = 0;
    // 清空选择
    tableRef.value.getTableRef().clearSelection();
  };

  // 新增
  const openDialog = (title = "新增", row?: QualityEvaluationVO) => {
    isEdit.value = title === "修改";
    if (row) {
      currentFormData.value = { ...row };
    } else {
      currentFormData.value = {} as QualityEvaluationVO;
    }
    dialogVisible.value = true;
  };

  // 编辑
  const handleUpdate = (row: QualityEvaluationVO) => {
    openDialog("修改", row);
  };

  // 删除
  const handleDelete = (row: QualityEvaluationVO) => {
    ElMessageBox.confirm(
      `确认要删除学号为 ${row.studentId} 的基本素质测评记录吗?`,
      "系统提示",
      {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }
    )
      .then(async () => {
        await deleteQualityEvaluation(row.evaluationId!);
        ElMessage.success("删除成功");
        onSearch();
      })
      .catch(() => {});
  };

  // 批量删除
  const onbatchDel = () => {
    if (multipleSelection.value.length === 0) {
      ElMessage.warning("请选择要删除的数据");
      return;
    }

    ElMessageBox.confirm(
      `确认要删除这 ${multipleSelection.value.length} 条记录吗?`,
      "系统提示",
      {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }
    )
      .then(async () => {
        const ids = multipleSelection.value.map(item => item.evaluationId!);
        await batchDeleteQualityEvaluation(ids);
        ElMessage.success("删除成功");
        onSearch();
        onSelectionCancel();
      })
      .catch(() => {});
  };

  // 导出数据
  const handleExport = async () => {
    try {
      const params: QualityEvaluationQueryDTO = { ...form };
      const response = await exportQualityEvaluation(params);

      // 创建下载链接
      const url = window.URL.createObjectURL(new Blob([response]));
      const link = document.createElement("a");
      link.href = url;
      link.setAttribute("download", `基本素质测评成绩_${dayjs().format("YYYY-MM-DD")}.xlsx`);
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);

      ElMessage.success("导出成功");
    } catch (error) {
      ElMessage.error("导出失败");
    }
  };

  // 下载模板
  const handleDownloadTemplate = async () => {
    try {
      const response = await downloadQualityEvaluationTemplate();

      // 创建下载链接
      const url = window.URL.createObjectURL(new Blob([response]));
      const link = document.createElement("a");
      link.href = url;
      link.setAttribute("download", "基本素质测评成绩导入模板.xlsx");
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);

      ElMessage.success("模板下载成功");
    } catch (error) {
      ElMessage.error("模板下载失败");
    }
  };

  // 导入数据
  const handleImport = async (file: File) => {
    try {
      const response = await importQualityEvaluation(file);
      ElMessage.success(response || "导入成功");
      onSearch(); // 刷新列表
    } catch (error: any) {
      console.error("导入失败:", error);
      ElMessage.error(error.response?.data?.message || "导入失败");
    }
    return false; // 阻止自动上传
  };

  onMounted(() => {
    // 异步加载数据，避免阻塞组件初始化
    loadSemesters();
    onSearch();
  });

  return {
    form,
    loading,
    columns,
    dataList,
    selectedNum,
    pagination,
    semesterOptions,
    deviceDetection,
    dialogVisible,
    currentFormData,
    isEdit,
    onSearch,
    resetForm,
    onbatchDel,
    openDialog,
    handleUpdate,
    handleDelete,
    handleExport,
    handleDownloadTemplate,
    handleImport,
    handleSizeChange,
    onSelectionCancel,
    handleCurrentChange,
    handleSelectionChange
  };
}
