import type { QualityEvaluationVO } from "@/api/score/qualityEvaluation";
import type { SemesterItem } from "@/api/basic/semester";

interface FormItemProps {
  /** 评估ID */
  evaluationId?: number;
  /** 用于判断是`新增`还是`修改` */
  title: string;
  /** 学号 */
  studentId: string;
  /** 学生姓名 */
  studentName: string;
  /** 班级名称 */
  className: string;
  /** 学期ID */
  semesterId?: number;
  /** 基础分 */
  periodScore: number;
  /** 加分 */
  addScore: number;
  /** 扣分 */
  reduceScore: number;
  /** 加分说明 */
  addScoreRemark: string;
  /** 扣分说明 */
  reduceScoreRemark: string;
  /** 总分 */
  totalScore: number;
  /** 学期选项 */
  semesterOptions: SemesterItem[];
}

interface FormProps {
  formInline: FormItemProps;
}

export type { FormItemProps, FormProps };
